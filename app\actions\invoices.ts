'use server';

import { getCurrentDbUser } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { redis } from '@/lib/redis';
import { prisma, withRetry } from '@/lib/db';
import { invoiceDownloadSchema, type InvoiceDownloadInput } from '@/lib/zod';
import { randomBytes } from 'crypto';
import { promises as fs } from 'fs';
import path from 'path';
import { headers } from 'next/headers';

export interface InvoiceDownloadResult {
  success: boolean;
  downloadUrl?: string;
  error?: string;
}

// Invoice files base path
const INVOICE_BASE_PATH = process.env.INVOICE_BASE_PATH;

/**
 * Generate a secure download token for an invoice
 */
export async function generateInvoiceDownloadToken(
  input: InvoiceDownloadInput
): Promise<InvoiceDownloadResult> {

  if (!INVOICE_BASE_PATH) {
    logger.error('[generateInvoiceDownloadToken] INVOICE_BASE_PATH environment variable is not set.');
    return { success: false, error: 'Configurare server incorectă.' };
  }

    // Normalize the base path to handle UNC paths properly
  const normalizedBasePath = path.normalize(INVOICE_BASE_PATH.replace(/^["']|["']$/g, ''));

  try {
    // Get request headers for audit logging
    const headersList = await headers();
    const userAgent = headersList.get('user-agent') || 'Unknown';
    const clientIp = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'Unknown';

    // Validate input
    const validationResult = invoiceDownloadSchema.safeParse(input);
    if (!validationResult.success) {
      logger.warn(`[generateInvoiceDownloadToken] Invalid input from IP: ${clientIp}:`, validationResult.error.format());
      return { success: false, error: 'Date de intrare invalide' };
    }

    const { orderId } = validationResult.data;

    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      logger.warn('[generateInvoiceDownloadToken] No authenticated user');
      return { success: false, error: 'Utilizator neautentificat' };
    }

    // Verify order ownership and get invoice details
    const order = await withRetry(() =>
      prisma.order.findFirst({
        where: {
          id: orderId,
          userId: user.id, // Ensure user owns the order
        },
        select: {
          id: true,
          orderNumber: true,
          invoiceAM: true,
          userId: true,
        },
      })
    );

    if (!order) {
      logger.warn(`[generateInvoiceDownloadToken] Order not found or access denied: ${orderId} for user: ${user.id}`);
      return { success: false, error: 'Comanda nu a fost găsită sau nu aveți acces la ea' };
    }

    if (!order.invoiceAM) {
      logger.warn(`[generateInvoiceDownloadToken] No invoice available for order: ${orderId}`);
      return { success: false, error: 'Nu există factură disponibilă pentru această comandă' };
    }

    // Check if invoice file exists
   const invoiceFileName = `${order.invoiceAM}.pdf`;
    const sanitizedFileName = path.basename(invoiceFileName);
    
    // Use the normalized base path
    const invoiceFilePath = path.join(normalizedBasePath, sanitizedFileName);
    
    // For UNC paths, we need to handle the resolution differently
    let resolvedPath: string;
    let resolvedBasePath: string;
    
    if (normalizedBasePath.startsWith('\\\\')) {
      // Handle UNC paths
      resolvedPath = path.resolve(invoiceFilePath);
      resolvedBasePath = path.resolve(normalizedBasePath);
    } else {
      // Handle regular paths
      resolvedPath = path.resolve(invoiceFilePath);
      resolvedBasePath = path.resolve(normalizedBasePath);
    }

    if (!resolvedPath.startsWith(resolvedBasePath)) {
      logger.error(`[generateInvoiceDownloadToken] Attempted path traversal: ${invoiceFileName} -> ${resolvedPath}`);
      return { success: false, error: 'Calea fișierului nu este validă' };
    }

    // Log the actual path being checked for debugging
    logger.info(`[generateInvoiceDownloadToken] Checking file access: ${invoiceFilePath}`);

    try {
      await fs.access(invoiceFilePath);
    } catch (error) {
      logger.error(`[generateInvoiceDownloadToken] Invoice file not found: ${invoiceFilePath}`, error);
      return { success: false, error: 'Fișierul facturii nu a fost găsit pe server' };
    }


    // Generate secure token
    const token = randomBytes(32).toString('hex');
    const tokenKey = `invoice_download:${token}`;

    // Store token data in Redis with 5-minute expiration
    const tokenData = {
      orderId: order.id,
      userId: user.id,
      invoiceAM: order.invoiceAM,
      orderNumber: order.orderNumber,
      filePath: invoiceFilePath,
      createdAt: new Date().toISOString(),
    };

    if (!redis) {
      logger.error('[generateInvoiceDownloadToken] Redis not available');
      return { success: false, error: 'Serviciul de descărcare nu este disponibil momentan' };
    }

    await redis.set(tokenKey, tokenData, { ex: 300 }); // 5 minutes TTL

    // Generate download URL
    const downloadUrl = `/api/invoices/download?tokenInvoice=${token}`;

    logger.info(`[generateInvoiceDownloadToken] Download token generated for order: ${order.orderNumber} by user: ${user.id}, IP: ${clientIp}, UserAgent: ${userAgent}`);

    return {
      success: true,
      downloadUrl,
    };
  } catch (error) {
    logger.error('[generateInvoiceDownloadToken] Error generating download token:', error);
    return { success: false, error: 'Eroare la generarea linkului de descărcare' };
  }
}
