"use client";

import { deleteItemFromCart } from "@/app/actions/cart";
import { Button } from "@/components/ui/button";
import { formatPriceRON } from "@/lib/utils";
import { CartItem } from "@/types/cart";
import { X } from "lucide-react";
import Image from "next/image";
import { useTransition } from "react";
import { toast } from "sonner";

export default function CartPreviewItem({ item }: { item: CartItem }) {

  const [isUpdating, startTransition] = useTransition();

  const handleUpdate = async () => {
    startTransition(async () => {
      try {
        const response = await deleteItemFromCart(item.id);

        if (response.success) {
          toast.success("Sters cu succes din cos.");
        } else {
          toast.error("Eșec la actualizare.");
        }
      } catch{
        toast.error("Nu s-a putut actualiza produsul.");
      }
    });
  };

  return (
    <div key={item.id} className="flex gap-3 pb-3 border-b border-gray-100 dark:border-gray-700">
      <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
         <Image
          src={`/api/images/${item.Material_Number}`}
          width={100}
          height={100}
          //src={item.ImageUrl || "/productDefault.jpg"}
          priority
          alt={item.Description_Local || ""}
          className="w-full h-full object-cover"
        /> 
      </div>

      <div className="flex-1 min-w-0 space-y-1">
        <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
          {item.Description_Local}
        </h4>
        <p className="text-xs text-gray-500 dark:text-gray-400">
          {item.Material_Number}
        </p>
        <p className="text-xs ">
          {item.quantity} x {formatPriceRON(item.FinalPrice)}
        </p>
      </div>

      <div className="flex flex-col items-end min-w-0">
        <Button
          disabled={isUpdating}
          variant="ghost"
          size="icon"
          className=""
          onClick={handleUpdate}
        >
          <div className="w-6 h-6 flex items-center justify-center rounded-full bg-white/30 backdrop-blur-sm hover:text-red-500 transition">
            <X className="w-4 h-4" />
          </div>
        </Button>
        <p className="text-xs font-medium truncate max-w-[80px]">
          {formatPriceRON((item.FinalPrice || 0) * item.quantity)}
        </p>
      </div>
    </div>
  );
}




// "use client"

// import { updateCartActiondata } from "@/app/actions/cart";
// import { Button } from "@/components/ui/button";
// import { formatPriceRON } from "@/lib/utils";
// import { CartItem } from "@/types/cart";
// import { Trash, X, XCircle } from "lucide-react";
// import Image from "next/image";
// import { useState } from "react";

// export default function CartPreviewItem({ item }: { item: CartItem }) {
//     const [item, setitem] = useState(item);
//     const [isUpdating, setIsUpdating] = useState(false);

//     const handleUpdate = async (updates: Partial<typeof item>) => {
//       setIsUpdating(true);
//       const updatedItem = { ...item, ...updates };
//       setitem(updatedItem); // Optimistically update the UI
//       await updateCartActiondata({ itemId: item.id, ...updates });
//       setIsUpdating(false); // Reset the state after the update is complete
//     };

//   return (
//             <div key={item.id} className="flex gap-3 pb-3 border-b border-gray-100 dark:border-gray-700">
//               <div className="w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
//                 <Image
//                   width={100}
//                   height={100}
//                   src={item.ImageUrl}
//                   alt={item.Description_Local}
//                   className="w-full h-full object-cover"
//                 />
//               </div>
//               <div className="flex-1 min-w-0">
//                 <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">{item.Description_Local}</h4>
//                 <p className="text-xs text-gray-500 dark:text-gray-400">{item.quantity} x {formatPriceRON(item.FinalPrice)}</p>
//                 <p className="text-sm font-medium">${formatPriceRON(item.FinalPrice * item.quantity)}</p>
//               </div>
//               <div className="flex flex-col gap-2">
//                 <Button disabled={isUpdating} variant="ghost" size="icon" className="gap-2" onClick={() => handleUpdate({ quantity: 0 })}>
//                  <div className="w-6 h-6 flex items-center justify-center rounded-full bg-white/30 backdrop-blur-sm hover:text-red-500  transition">
//                   <X className="w-4 h-4" />
//                   </div>
//                 </Button>
//               </div>
//             </div>
//             )
// }
