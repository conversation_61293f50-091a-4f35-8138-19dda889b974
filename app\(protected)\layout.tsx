"server-only"

import type { <PERSON>ada<PERSON> } from "next";
import "../globals.css";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { getCurrentDbUser } from "@/lib/auth";
import { getWishlistProductCodes } from "../getData/wishlist";
import { Providers } from "../providers";
import { WishlistProvider } from "../context/WishlistContext";
import { MainNavbar } from "../components/navbar/MainNavbar";
import Footer from "../components/footer/Footer";
import { Toaster } from "@/components/ui/sonner";
import { getCart } from "../getData/cart";
import { Cart, CartItem } from "@/types/cart";

export const metadata: Metadata = {
  title: "Automobile Bavaria : Piese auto BMW originale",
  description: "Piese auto BMW originale",
};


export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {

 // Fetch user once at the root level
  const user = await getCurrentDbUser();

  let wishlistProductCodes = new Set<string>();
  let cart: Cart | null = null;

  if (user) {
    const [wishlistData, cartData] = await Promise.all([
      getWishlistProductCodes(user.id),
      getCart(user.id)
    ]);
    
    wishlistProductCodes = wishlistData;
    cart = cartData;
  }

  const cartItems: CartItem[] = cart?.items.filter((item) => item.quantity > 0) || [];

  return (
    <ClerkProvider>
          <Providers>
            <WishlistProvider initialItems={wishlistProductCodes}>
                <div className="sticky top-0 z-50 w-full shadow-md bg-white dark:bg-gray-900">
                  <div className="max-w-[1640px] mx-auto">
                    <MainNavbar  
                      user={user}
                      cartItems={cartItems}
                    />
                    
                  </div>
                </div>
                <div className="relative w-full">
                  {children}
                  <Footer />
                </div>
                <Toaster />
            </WishlistProvider>
          </Providers>
    </ClerkProvider>
  );
}


