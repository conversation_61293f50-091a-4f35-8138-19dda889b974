import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    const { userId, reason } = await request.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Update user suspension status
    // const updatedUser = await prisma.user.update({
    //   where: { id: userId },
    //   data: {
    //     isSuspended: true,
    //     suspensionReason: reason || 'Admin suspension',
    //     updatedAt: new Date(),
    //   }
    // });

    // Create audit log
    await prisma.userAuditLog.create({
      data: {
        userId: userId,
        action: 'account.suspend',
        entityType: 'user',
        entityId: userId,
        details: {
          reason: reason || 'Admin suspension',
          suspendedAt: new Date().toISOString(),
          suspendedBy: 'admin'
        },
        ipAddress: request.headers.get('x-forwarded-for') || null,
        userAgent: request.headers.get('user-agent') || null,
      }
    });

    logger.info(`[admin] User ${userId} suspended by admin`);

    return NextResponse.json({ 
      success: true, 
      message: 'User suspended successfully' 
    });

  } catch (error) {
    logger.error('[admin] Error suspending user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
