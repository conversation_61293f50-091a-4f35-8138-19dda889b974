import { NextRequest, NextResponse } from 'next/server'
import { getSearchSuggestions } from '@/app/getData/search'
import { z } from 'zod'
import { logger } from '@/lib/logger'

// Validation schema
const searchQuerySchema = z.object({
  query: z.string().min(3, 'Query must be at least 3 characters').max(100, 'Query too long')
})

export async function GET(request: NextRequest) {
  try {
    
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')

    // Validate query parameter
    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter is required' },
        { status: 400 }
      )
    }

    // Validate query with Zod
    const validationResult = searchQuerySchema.safeParse({ query })
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameter',
          details: validationResult.error.errors
        },
        { status: 400 }
      )
    }

    // Get search suggestions
    const suggestions = await getSearchSuggestions(validationResult.data.query)

    // Return successful response
    return NextResponse.json(suggestions, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })

  } catch (error) {
    logger.error('Error in search suggestions API:', error)
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to fetch search suggestions'
      },
      { status: 500 }
    )
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  )
}
