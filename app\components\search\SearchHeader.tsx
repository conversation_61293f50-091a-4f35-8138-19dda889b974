"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { X, Filter } from "lucide-react"
import { SearchFilters, SearchPageData } from "@/types/search"

interface SearchHeaderProps {
  query?: string
  totalResults: number
  appliedFilters: SearchFilters
  searchData: SearchPageData
}

export default function SearchHeader({ query, totalResults, appliedFilters, searchData }: SearchHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSortChange = (sort: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('sort', sort)
    params.delete('page') // Reset to first page when sorting
    // Always navigate to search route
    router.push(`/search?${params.toString()}`)
  }

  const removeFilter = (filterType: string, value?: string, attributeKey?: string) => {
    const params = new URLSearchParams(searchParams.toString())

    switch (filterType) {
      case 'query':
        params.delete('query')
        break

      case 'category3':
        params.delete('category3')
        params.delete('category3Id')
        break
      case 'brand':
        if (value) {
          const brands = params.get('brands')?.split(',').filter(b => b !== value) || []
          if (brands.length > 0) {
            params.set('brands', brands.join(','))
          } else {
            params.delete('brands')
          }
        }
        break
      case 'class':
        if (value) {
          const classes = params.get('classes')?.split(',').filter(c => c !== value) || []
          if (classes.length > 0) {
            params.set('classes', classes.join(','))
          } else {
            params.delete('classes')
          }
        }
        break
      case 'price':
        params.delete('minPrice')
        params.delete('maxPrice')
        break
      case 'discount':
        params.delete('hasDiscount')
        break
      case 'attributes':
        if (attributeKey && value && appliedFilters.attributes) {
          const currentAttributes = { ...appliedFilters.attributes }
          const currentValues = currentAttributes[attributeKey] || []
          const newValues = currentValues.filter(v => v !== value)

          if (newValues.length === 0) {
            delete currentAttributes[attributeKey]
          } else {
            currentAttributes[attributeKey] = newValues
          }

          // Convert back to URL format
          const attributesString = Object.entries(currentAttributes)
            .map(([k, values]) => `${k}:${values.join(',')}`)
            .join('|')

          if (attributesString) {
            params.set('attributes', attributesString)
          } else {
            params.delete('attributes')
          }
        } else {
          params.delete('attributes')
        }
        break
    }

    params.delete('page') // Reset to first page
    // Always navigate to search route
    router.push(`/search?${params.toString()}`)
  }

  const clearAllFilters = () => {
    const params = new URLSearchParams()
    const query = searchParams.get('query')
    if (query) params.set('query', query)

    // Always navigate to search route
    router.push(`/search?${params.toString()}`)
  }

  const hasActiveFilters = !!(
    appliedFilters.category3 ||
    appliedFilters.category3Id ||
    appliedFilters.brands?.length ||
    appliedFilters.classes?.length ||
    (appliedFilters.attributes && Object.keys(appliedFilters.attributes).length > 0) ||
    appliedFilters.minPrice ||
    appliedFilters.maxPrice ||
    appliedFilters.hasDiscount
  )

  return (
    <div className="mb-6">
      {/* Results count and sort */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {query
              ? `Rezultate pentru "${query}"`
              : 'Rezultate cautare'
            }
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {totalResults.toLocaleString('ro-RO')} produse gasite
          </p>
        </div>

        <Select value={appliedFilters.sort || 'relevance'} onValueChange={handleSortChange}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="relevance">Relevanta</SelectItem>
            {/* <SelectItem value="price_asc">Pret crescator</SelectItem>
            <SelectItem value="price_desc">Pret descrescator</SelectItem>
            <SelectItem value="discount_desc">Reducere mare</SelectItem> */}
            <SelectItem value="name_asc">Nume A-Z</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Applied filters */}
      <div className="flex flex-wrap items-center gap-2">
        {(appliedFilters.category3 || searchData.selectedCategory) && (
          <Badge variant="active" className="flex items-center gap-1">
            Subcategorie: {searchData.selectedCategory?.name || appliedFilters.category3}
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('category3')}
            />
          </Badge>
        )}

        {appliedFilters.brands?.map(brand => (
          <Badge key={brand} variant="active" className="flex items-center gap-1">
            Brand: {brand}
            <X 
              className="h-4 w-4 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('brand', brand)}
            />
          </Badge>
        ))}

        {appliedFilters.classes?.map(cls => (
          <Badge key={cls} variant="active" className="flex items-center gap-1">
            Clasa: {cls}
            <X 
              className="h-4 w-4 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('class', cls)}
            />
          </Badge>
        ))}

        {(appliedFilters.minPrice || appliedFilters.maxPrice) && (
          <Badge variant="active" className="flex items-center gap-1">
            Pret: {appliedFilters.minPrice || 0} - {appliedFilters.maxPrice || '∞'} RON
            <X 
              className="h-4 w-4 cursor-pointer hover:text-red-500" 
              onClick={() => removeFilter('price')}
            />
          </Badge>
        )}

        {/* Attribute filters */}
        {appliedFilters.attributes && Object.entries(appliedFilters.attributes).map(([key, values]) =>
          values.map((value) => (
            <Badge key={`${key}-${value}`} variant="active" className="flex items-center gap-1">
              {key}: {value}
              <X
                className="h-4 w-4 cursor-pointer hover:text-red-500"
                onClick={() => removeFilter('attributes', value, key)}
              />
            </Badge>
          ))
        ).flat()}

        {appliedFilters.hasDiscount && (
          <Badge variant="active" className="flex items-center gap-1">
            Cu discount
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('discount')}
            />
          </Badge>
        )}

        {hasActiveFilters && (
          <Badge
            variant="delete"
            onClick={clearAllFilters}
            className="flex items-center gap-1 text-xs cursor-pointer"
          >
            <Filter className="h-4 w-4" />
            Reseteaza filtrele
          </Badge>
        )}
      </div>
    </div>
  )
}
