// app/api/images/[productName]/count/route.ts
import { NextRequest, NextResponse } from "next/server";
import { findProductImages } from "@/app/getData/products";

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ productName: string }> }
) {
  try {
    const { productName } = await params;
    const images = await findProductImages(productName);
    
    return NextResponse.json({ 
      count: images.length,
      images: images.map((img: string, index: number) => ({
        index,
        filename: img,
        url: `/api/images/${productName}/${index}`
      }))
    });
  } catch (error) {
    return NextResponse.json({ count: 0, images: [] });
  }
}