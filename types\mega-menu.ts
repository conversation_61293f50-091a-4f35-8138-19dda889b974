/**
 * TypeScript interfaces for the mega-menu component system
 * Defines the data structure for category hierarchy display
 */

export interface MegaMenuLevel3 {
  id: string
  name: string
  nameRO: string 
  slug: string
  productCount: number
}

export interface MegaMenuLevel2 {
  id: string
  name: string
  nameRO: string 
  slug: string
  level3Categories: MegaMenuLevel3[]
}

export interface MegaMenuCategory {
  id: string
  name: string
  nameRO: string
  slug: string
  level2Categories: MegaMenuLevel2[]
}

/**
 * Props for MegaMenu component
 */
export interface MegaMenuProps {
  categories: MegaMenuCategory[]
  className?: string
}

/**
 * Props for MegaMenuItem component (individual level 1 category trigger)
 */
export interface MegaMenuItemProps {
  category: MegaMenuCategory
  isActive: boolean
  onHover: (categoryId: string | null) => void
  className?: string
}

/**
 * Props for MegaMenuContent component (the dropdown panel)
 */
export interface MegaMenuContentProps {
  category: MegaMenuCategory | null
  isVisible: boolean
  onClose: () => void
  className?: string
}

/**
 * Props for MobileNavigation component
 */
export interface MobileNavigationProps {
  categories: MegaMenuCategory[]
  isOpen: boolean
  onClose: () => void
}

/**
 * Props for MobileCategoryList component
 */
export interface MobileCategoryListProps {
  categories: MegaMenuCategory[]
  onCategoryClick?: (url: string) => void
}

/**
 * State interface for mega-menu hover management
 */
export interface MegaMenuState {
  activeCategory: string | null
  isVisible: boolean
  hoverTimeout: NodeJS.Timeout | null
}

/**
 * Configuration for mega-menu animations
 */
export interface MegaMenuAnimationConfig {
  hoverDelay: number
  hideDelay: number
  animationDuration: number
}

/**
 * Utility type for category URL generation
 */
export interface CategoryUrlParams {
  level2Name: string
  level3Name?: string
  level3Id?: string
}
