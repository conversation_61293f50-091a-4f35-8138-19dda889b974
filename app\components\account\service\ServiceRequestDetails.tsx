"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  Calendar,
  Clock,
  FileText,
  MapPin,
  Package,
  XCircle,
  Mail,
  Phone,
} from "lucide-react";
import { ServiceRequest } from "@/types/services";
import { formatDate } from "@/lib/order-utils";
import { useTransition } from "react";
import { cancelServiceRequest } from "@/app/actions/services";
import { toast } from "sonner";
import { ServiceStatusBadge, canCancelServiceRequest } from "./ServiceStatusBadge";
import Image from "next/image";

interface ServiceRequestDetailsProps {
  serviceRequest: ServiceRequest | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onServiceRequestUpdated?: () => void;
}

export default function ServiceRequestDetails({ 
  serviceRequest, 
  open,
  onOpenChange,
  onServiceRequestUpdated
}: ServiceRequestDetailsProps) {
  const [isPending, startTransition] = useTransition();

  if (!serviceRequest) {
    return null;
  }

  // const statusDisplay = getServiceStatusDisplay(serviceRequest.status); // Not used currently

  const handleCancelService = () => {
    if (!confirm('Ești sigur că vrei să anulezi această cerere de service?')) {
      return;
    }

    startTransition(async () => {
      const result = await cancelServiceRequest(serviceRequest.id);
      if (result.success) {
        toast.success('Cererea de service a fost anulată cu succes');
        onOpenChange(false);
        onServiceRequestUpdated?.();
      } else {
        toast.error(result.error || 'Eroare la anularea cererii de service');
      }
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <span>Cerere Service #{serviceRequest.serviceNumber}</span>
            <ServiceStatusBadge status={serviceRequest.status} />
          </DialogTitle>
          <DialogDescription>
            Detalii complete pentru cererea de service
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Service Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-4">
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Creat la:</span>
                <span className="font-medium">{formatDate(serviceRequest.createdAt)}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Package className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Comandă:</span>
                <span className="font-medium">#{serviceRequest.orderNumber}</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-muted-foreground">Email-uri trimise:</span>
                <span className="font-medium">{serviceRequest.emailsSent}</span>
              </div>
            </div>
            <div className="space-y-2">
              {serviceRequest.lastEmailSentAt && (
                <div className="flex items-center gap-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Ultimul email:</span>
                  <span className="font-medium">{formatDate(serviceRequest.lastEmailSentAt)}</span>
                </div>
              )}
              {serviceRequest.resolvedAt && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Rezolvat la:</span>
                  <span className="font-medium">{formatDate(serviceRequest.resolvedAt)}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Product Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <Package className="h-5 w-5" />
              Produs pentru Service
            </h3>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-start gap-4">
                {serviceRequest.productImage && (
                  <Image
                    src={`/api/images/${serviceRequest.productCode}`}
                    alt={serviceRequest.productName || "Produs"}
                    width={100}
                    height={100}
                    className="w-16 h-16 object-cover rounded-md"
                  />
                )}
                <div className="flex-1">
                  <h4 className="font-medium text-lg">{serviceRequest.productName}</h4>
                  <p className="text-sm text-muted-foreground font-mono">
                    Cod: {serviceRequest.productCode}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Issue Details */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Detalii Problemă
            </h3>
            <div className="space-y-3">
              <div>
                <span className="text-sm text-muted-foreground">Tip problemă:</span>
                <Badge variant="outline" className="ml-2">
                  {serviceRequest.issueType === 'DEFECTIVE_PART' ? 'Piesă defectă' :
                   serviceRequest.issueType === 'DAMAGED_IN_SHIPPING' ? 'Deteriorat în transport' :
                   serviceRequest.issueType === 'WRONG_ITEM_RECEIVED' ? 'Produs greșit primit' :
                   serviceRequest.issueType === 'MISSING_PARTS' ? 'Piese lipsă' :
                   serviceRequest.issueType === 'NOT_AS_DESCRIBED' ? 'Nu corespunde descrierii' :
                   serviceRequest.issueType === 'COMPATIBILITY_ISSUE' ? 'Problemă compatibilitate' :
                   'Altele'}
                </Badge>
              </div>
              <div>
                <span className="text-sm text-muted-foreground">Descriere:</span>
                <p className="mt-1 p-3 bg-muted/50 rounded-md text-sm">
                  {serviceRequest.description}
                </p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Adresa de Service
            </h3>
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Metodă:</span>
                  <span className="font-medium">
                    {serviceRequest.method === 'curier' && 'Curier'}
                    {serviceRequest.method === 'intern' && 'Transport intern'}
                    {serviceRequest.method === 'showroom' && 'Ridicare showroom'}
                  </span>
                </div>

                {serviceRequest.method === 'curier' && serviceRequest.address && (
                  <>
                    <div className="border-t pt-3 space-y-2">
                      <p className="font-medium">{serviceRequest.address.fullName}</p>
                      <p className="text-sm">{serviceRequest.address.address}</p>
                      <p className="text-sm text-muted-foreground">
                        {serviceRequest.address.city}, {serviceRequest.address.county}
                      </p>
                      {serviceRequest.address.phoneNumber && (
                        <div className="flex items-center gap-2 text-sm">
                          <Phone className="h-4 w-4" />
                          <span>{serviceRequest.address.phoneNumber}</span>
                        </div>
                      )}
                      {serviceRequest.address.notes && (
                        <p className="text-sm text-muted-foreground">
                          Note: {serviceRequest.address.notes}
                        </p>
                      )}
                    </div>
                  </>
                )}

                {serviceRequest.method === 'showroom' && serviceRequest.showroom && (
                  <>
                    <div className="border-t pt-3 space-y-2">
                      <p className="font-medium">{serviceRequest.showroom.name}</p>
                      <p className="text-sm">{serviceRequest.showroom.address1}</p>
                      {serviceRequest.showroom.address2 && (
                        <p className="text-sm">{serviceRequest.showroom.address2}</p>
                      )}
                      <p className="text-sm text-muted-foreground">
                        {serviceRequest.showroom.city}, {serviceRequest.showroom.county}
                      </p>
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-4 w-4" />
                        <span>{serviceRequest.showroom.phone}</span>
                      </div>
                      {serviceRequest.showroom.email && (
                        <div className="flex items-center gap-2 text-sm">
                          <Mail className="h-4 w-4" />
                          <span>{serviceRequest.showroom.email}</span>
                        </div>
                      )}
                      {serviceRequest.showroom.program && (
                        <p className="text-sm text-muted-foreground">
                          Program: {serviceRequest.showroom.program}
                        </p>
                      )}
                    </div>
                  </>
                )}

                {serviceRequest.method === 'intern' && (
                  <div className="border-t pt-3">
                    <p className="text-sm text-muted-foreground">
                      Produsul va fi ridicat prin serviciul intern de transport - nu sunt necesare date suplimentare.
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Resolution Details */}
          {(serviceRequest.resolution || serviceRequest.resolutionNotes) && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Rezoluție
                </h3>
                <div className="space-y-3">
                  {serviceRequest.resolution && (
                    <div>
                      <span className="text-sm text-muted-foreground">Tip rezoluție:</span>
                      <Badge variant="outline" className="ml-2">
                        {serviceRequest.resolution === 'REPLACEMENT' ? 'Înlocuire' :
                         serviceRequest.resolution === 'REFUND' ? 'Rambursare' :
                         serviceRequest.resolution === 'REPAIR' ? 'Reparație' :
                         serviceRequest.resolution === 'PARTIAL_REFUND' ? 'Rambursare parțială' :
                         'Fără acțiune'}
                      </Badge>
                    </div>
                  )}
                  {serviceRequest.resolutionNotes && (
                    <div>
                      <span className="text-sm text-muted-foreground">Note rezoluție:</span>
                      <p className="mt-1 p-3 bg-muted/50 rounded-md text-sm">
                        {serviceRequest.resolutionNotes}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </>
          )}

          {/* Status History */}
          {serviceRequest.statusHistory && serviceRequest.statusHistory.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Istoric Status
                </h3>
                <div className="space-y-3">
                  {serviceRequest.statusHistory.map((history) => (
                    <div key={history.id} className="flex items-start gap-3 p-3 bg-muted/50 rounded-md">
                      <div className="w-2 h-2 rounded-full bg-primary mt-2 shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <ServiceStatusBadge status={history.newStatus} />
                          <span className="text-xs text-muted-foreground">
                            {formatDate(history.changedAt)}
                          </span>
                        </div>
                        {history.notes && (
                          <p className="text-sm text-muted-foreground">{history.notes}</p>
                        )}
                        {history.emailSent && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
                            <Mail className="h-3 w-3" />
                            <span>Email trimis</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t">
          {canCancelServiceRequest(serviceRequest.status) && (
            <Button
              variant="destructive"
              onClick={handleCancelService}
              disabled={isPending}
              className="flex items-center gap-2"
            >
              <XCircle className="h-4 w-4" />
              {isPending ? "Se anulează..." : "Anulează cererea"}
            </Button>
          )}
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Închide
          </Button>
        </div>

        </div>
      </DialogContent>
    </Dialog>
  );
}
