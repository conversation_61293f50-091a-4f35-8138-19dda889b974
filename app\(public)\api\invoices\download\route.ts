import { NextRequest, NextResponse } from 'next/server';
import { getCurrentDbUser } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { redis } from '@/lib/redis';
import { promises as fs } from 'fs';
import { headers } from 'next/headers';

interface TokenData {
  orderId: string;
  userId: string;
  invoiceAM: string;
  orderNumber: string;
  filePath: string;
  createdAt: string;
}

export async function GET(request: NextRequest) {
  try {
    const headersList = await headers();
    const userAgent = headersList.get('user-agent') || 'Unknown';
    const clientIp = headersList.get('x-forwarded-for') || headersList.get('x-real-ip') || 'Unknown';

    // Get token from query parameters
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('tokenInvoice');

    if (!token) {
      logger.warn(`[Invoice Download] Missing token from IP: ${clientIp}`);
      return NextResponse.json(
        { error: 'Token lipsă' },
        { status: 400 }
      );
    }

    // Validate token format (64 hex characters)
    if (!/^[a-f0-9]{64}$/i.test(token)) {
      logger.warn(`[Invoice Download] Invalid token format from IP: ${clientIp}`);
      return NextResponse.json(
        { error: 'Token invalid' },
        { status: 400 }
      );
    }

    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      logger.warn(`[Invoice Download] Unauthenticated request from IP: ${clientIp}`);
      return NextResponse.json(
        { error: 'Utilizator neautentificat' },
        { status: 401 }
      );
    }

    // Check Redis availability
    if (!redis) {
      logger.error('[Invoice Download] Redis not available');
      return NextResponse.json(
        { error: 'Serviciul de descărcare nu este disponibil' },
        { status: 503 }
      );
    }

    // Get token data from Redis
    const tokenKey = `invoice_download:${token}`;
    const tokenData = await redis.get<TokenData>(tokenKey);

    if (!tokenData) {
      logger.warn(`[Invoice Download] Invalid or expired token from user: ${user.id}, IP: ${clientIp}`);
      return NextResponse.json(
        { error: 'Token invalid sau expirat' },
        { status: 401 }
      );
    }

    // Verify token belongs to current user
    if (tokenData.userId !== user.id) {
      logger.warn(`[Invoice Download] Token ownership mismatch. Token user: ${tokenData.userId}, Current user: ${user.id}, IP: ${clientIp}`);
      return NextResponse.json(
        { error: 'Acces interzis' },
        { status: 403 }
      );
    }

    // Delete token immediately for single-use security
    await redis.del(tokenKey);

    // Check if file exists
    try {
      await fs.access(tokenData.filePath);
    } catch (error) {
      logger.error(`[Invoice Download] File not found: ${tokenData.filePath} for order: ${tokenData.orderNumber}`, error);
      return NextResponse.json(
        { error: 'Fișierul facturii nu a fost găsit' },
        { status: 404 }
      );
    }

    // Read file
    let fileBuffer: Buffer;
    try {
      fileBuffer = await fs.readFile(tokenData.filePath);
    } catch (error) {
      logger.error(`[Invoice Download] Error reading file: ${tokenData.filePath}`, error);
      return NextResponse.json(
        { error: 'Eroare la citirea fișierului' },
        { status: 500 }
      );
    }

    // Log successful download
    logger.info(`[Invoice Download] Successful download: ${tokenData.invoiceAM}.pdf for order: ${tokenData.orderNumber} by user: ${user.id}, IP: ${clientIp}, UserAgent: ${userAgent}`);

    // Return file with appropriate headers
    const fileName = `Factura_${tokenData.orderNumber}_${tokenData.invoiceAM}.pdf`;
    const encodedFileName = encodeURIComponent(fileName);
    
    return new NextResponse(fileBuffer as BodyInit, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        //'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Disposition': `attachment; filename="${fileName}"; filename*=UTF-8''${encodedFileName}`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        // Security headers
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
      },
    });
  } catch (error) {
    logger.error('[Invoice Download] Unexpected error:', error);
    return NextResponse.json(
      { error: 'Eroare internă de server' },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Metodă nepermisă' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Metodă nepermisă' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Metodă nepermisă' },
    { status: 405 }
  );
}
