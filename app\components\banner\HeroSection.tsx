"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import {
  motion,
  useScroll,
  useTransform,
  AnimatePresence,
} from "framer-motion";
import { HeroBanner } from "@/app/getData/banners";

export default function HeroSection({
  heroBanners,
}: {
  heroBanners: HeroBanner[];
}) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 500], [0, 150]);

  useEffect(() => {
    if (heroBanners.length <= 1) return;
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroBanners.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [heroBanners.length]);

  const getTextAlignment = (position: string) => {
    switch (position) {
      case "LEFT":
        return "items-start text-left pl-8 md:pl-16";
      case "RIGHT":
        return "items-end text-right pr-8 md:pr-16";
      default:
        return "items-center text-center";
    }
  };

  if (heroBanners.length === 0) {
    return null;
  }

  return (
    <div className="relative w-full h-[600px] overflow-hidden bg-gray-900">
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1 }}
          style={{ y }}
          className="absolute inset-0 w-full h-[120%] -top-[10%]"
        >
          <Image
            src={`/api/images/${heroBanners[currentSlide].imageUrl}`}
            alt={heroBanners[currentSlide].title || "Hero banner"}
            fill
            sizes="100vw"
            priority
            className="object-cover object-center transition-transform duration-300"
          />
          <div className="absolute inset-0 bg-black/40" />
        </motion.div>
      </AnimatePresence>

      {/* Banner text */}
      <div
        className={`relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center ${
          getTextAlignment(heroBanners[currentSlide].textAlignment || "center")
        }`}
      >
        <motion.h1
          key={`title-${currentSlide}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg"
        >
          {heroBanners[currentSlide].title}
        </motion.h1>
        <motion.p
          key={`subtitle-${currentSlide}`}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-xl md:text-2xl text-white/70 max-w-2xl drop-shadow-lg"
        >
          {heroBanners[currentSlide].subtitle}
        </motion.p>
      </div>

      {/* Slide indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
        {heroBanners.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              index === currentSlide ? "w-8 bg-white" : "bg-white/50"
            }`}
          />
        ))}
      </div>
    </div>
  );
}


// import React, { useState, useEffect } from "react";
// import {
//   motion,
//   useScroll,
//   useTransform,
//   AnimatePresence,
// } from "framer-motion";

// interface HeroSlide {
//   imageUrl: string;
//   title: string;
//   subtitle: string;
//   textPosition: "left" | "center" | "right";
// }

// const slides: HeroSlide[] = [
//   {
//     imageUrl:
//       "https://images.unsplash.com/photo-1555215695-3004980ad54e?q=80&w=2070",
//     title: "Premium BMW Parts",
//     subtitle: "Authentic Parts for Ultimate Performance",
//     textPosition: "center",
//   },
//   {
//     imageUrl:
//       "https://images.unsplash.com/photo-1603584173870-7f23fdae1b7a?q=80&w=2070",
//     title: "M Performance",
//     subtitle: "Elevate Your Driving Experience",
//     textPosition: "left",
//   },
//   {
//     imageUrl:
//       "https://images.unsplash.com/photo-1617814076367-b759c7d7e738?q=80&w=2070",
//     title: "Engineering Excellence",
//     subtitle: "Precision in Every Detail",
//     textPosition: "right",
//   },
//   {
//     imageUrl:
//       "https://images.unsplash.com/photo-1606664515524-ed2f786a0bd6?q=80&w=2070",
//     title: "Ultimate Driving",
//     subtitle: "Parts That Define Performance",
//     textPosition: "center",
//   },
// ];

// const HeroSection: React.FC = () => {
//   const [currentSlide, setCurrentSlide] = useState(0);
//   const { scrollY } = useScroll();
//   const y = useTransform(scrollY, [0, 500], [0, 150]);

//   useEffect(() => {
//     const timer = setInterval(() => {
//       setCurrentSlide((prev) => (prev + 1) % slides.length);
//     }, 4000);

//     return () => clearInterval(timer);
//   }, []);

//   const getTextAlignment = (position: string) => {
//     switch (position) {
//       case "left":
//         return "items-start text-left pl-8 md:pl-16";
//       case "right":
//         return "items-end text-right pr-8 md:pr-16";
//       default:
//         return "items-center text-center";
//     }
//   };

//   return (
//     <div className="relative w-full h-[600px] overflow-hidden bg-gray-900">
//       <AnimatePresence mode="wait">
//         <motion.div
//           key={currentSlide}
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           exit={{ opacity: 0 }}
//           transition={{ duration: 1 }}
//           style={{ y }}
//           className="absolute inset-0 w-full h-[120%] -top-[10%]"
//         >
//           <div
//             className="absolute inset-0 bg-cover bg-center transition-transform duration-300"
//             style={{
//               backgroundImage: `url(${slides[currentSlide].imageUrl})`,
//             }}
//           >
//             <div className="absolute inset-0 bg-black/40" />
//           </div>
//         </motion.div>
//       </AnimatePresence>

//       <div
//         className={`relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center ${getTextAlignment(slides[currentSlide].textPosition)}`}
//       >
//         <motion.h1
//           key={`title-${currentSlide}`}
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6 }}
//           className="text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg"
//         >
//           {slides[currentSlide].title}
//         </motion.h1>
//         <motion.p
//           key={`subtitle-${currentSlide}`}
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6, delay: 0.2 }}
//           className="text-xl md:text-2xl text-gray-200 max-w-2xl drop-shadow-lg"
//         >
//           {slides[currentSlide].subtitle}
//         </motion.p>
//       </div>

//       {/* Slide indicators */}
//       <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
//         {slides.map((_, index) => (
//           <button
//             key={index}
//             onClick={() => setCurrentSlide(index)}
//             className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentSlide ? "w-8 bg-white" : "bg-white/50"}`}
//           />
//         ))}
//       </div>
//     </div>
//   );
// };

// export default HeroSection;


//THIS WORKS and it was being used
// import React, { useState, useEffect } from "react";
// import {
//   motion,
//   useScroll,
//   useTransform,
//   AnimatePresence,
// } from "framer-motion";
// import { HeroBanner } from "@/app/getData/banners";

// export default function HeroSection (  {heroBanners} : {heroBanners: HeroBanner[]}
// ){
//   const [currentSlide, setCurrentSlide] = useState(0);
//   const { scrollY } = useScroll();
//   const y = useTransform(scrollY, [0, 500], [0, 150]);

//   useEffect(() => {
//     const timer = setInterval(() => {
//       setCurrentSlide((prev) => (prev + 1) % heroBanners.length);
//     }, 4000);

//     return () => clearInterval(timer);
//   });

//   const getTextAlignment = (position: string) => {
//     switch (position) {
//       case "LEFT":
//         return "items-start text-left pl-8 md:pl-16";
//       case "RIGHT":
//         return "items-end text-right pr-8 md:pr-16";
//       default:
//         return "items-center text-center";
//     }
//   };

//   if(heroBanners.length === 0){
//     return null
//   }

//   return (
//     <div className="relative w-full h-[600px] overflow-hidden bg-gray-900">
//       <AnimatePresence mode="wait">
//         <motion.div
//           key={currentSlide}
//           initial={{ opacity: 0 }}
//           animate={{ opacity: 1 }}
//           exit={{ opacity: 0 }}
//           transition={{ duration: 1 }}
//           style={{ y }}
//           className="absolute inset-0 w-full h-[120%] -top-[10%]"
//         >
//           <div
//             className="absolute inset-0 bg-cover bg-center transition-transform duration-300"
//             style={{
//               //backgroundImage: `url(${heroBanners[currentSlide].imageUrl})`,
//               backgroundImage: `url(/api/images/${heroBanners[currentSlide].imageUrl})`,
//             }}
//           >
//             <div className="absolute inset-0 bg-black/40" />
//           </div>
//         </motion.div>
//       </AnimatePresence>

//       <div
//         className={`relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center ${getTextAlignment(heroBanners[currentSlide].textAlignment || 'center')}`}
//       >
//         <motion.h1
//           key={`title-${currentSlide}`}
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6 }}
//           className="text-4xl md:text-6xl font-bold text-white mb-4 drop-shadow-lg"
//         >
//           {heroBanners[currentSlide].title}
//         </motion.h1>
//         <motion.p
//           key={`subtitle-${currentSlide}`}
//           initial={{ opacity: 0, y: 20 }}
//           animate={{ opacity: 1, y: 0 }}
//           transition={{ duration: 0.6, delay: 0.2 }}
//           className="text-xl md:text-2xl text-white/70 max-w-2xl drop-shadow-lg"
//         >
//           {heroBanners[currentSlide].subtitle}
//         </motion.p>
//       </div>

//       {/* Slide indicators */}
//       <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
//         {heroBanners.map((_, index) => (
//           <button
//             key={index}
//             onClick={() => setCurrentSlide(index)}
//             className={`w-2 h-2 rounded-full transition-all duration-300 ${index === currentSlide ? "w-8 bg-white" : "bg-white/50"}`}
//           />
//         ))}
//       </div>
//     </div>
//   );
// };
