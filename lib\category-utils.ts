/**
 * Utility functions for category URL generation and manipulation
 * Client-safe utilities that don't import server-only dependencies
 */

/**
 * Helper function to generate category URL for routing
 * Uses slugs directly from the database for accurate routing
 */
export function generateCategoryUrl(level2Slug: string, level3Slug?: string): string {
  if (level3Slug) {
    return `/category/${level2Slug}?category3=${level3Slug}`
  }

  return `/category/${level2Slug}`
}

/**
 * Legacy function to convert names to slugs (for backward compatibility)
 * Use this only when you have names but need to generate slugs
 */
export function nameToSlug(name: string): string {
  return name.toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple consecutive hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading and trailing hyphens
    .trim()
}

/**
 * Extract category slug from URL
 */
export function extractCategorySlug(url: string): { level2?: string; level3?: string } {
  const match = url.match(/\/category\/([^?]+)/)
  if (!match) return {}

  const level2 = match[1]
  const urlParams = new URLSearchParams(url.split('?')[1] || '')
  const level3 = urlParams.get('category3') || undefined

  return { level2, level3 }
}

/**
 * Format category name for display
 */
export function formatCategoryName(name: string, nameRO?: string | null): string {
  return nameRO || name
}

/**
 * Generate category breadcrumb
 */
export function generateCategoryBreadcrumb(
  level1Name: string,
  level2Name?: string,
  level3Name?: string
): Array<{ name: string; url?: string }> {
  const breadcrumb = [{ name: level1Name, url: '/' }]

  if (level2Name) {
    breadcrumb.push({
      name: level2Name,
      url: generateCategoryUrl(level2Name)
    })
  }

  if (level3Name && level2Name) {
    breadcrumb.push({
      name: level3Name,
      url: generateCategoryUrl(level2Name, level3Name)
    })
  }

  return breadcrumb
}
