"server-only"

import { CartItem } from "@/types/cart";
import { User } from "@/generated/prisma";
import { getMegaMenuCategories } from "@/app/getData/categories";
import { MainNavbarClient } from "./MainNavbarClient";

export type AccountComponentProps = {
  firstName: string;
  email: string;
  profileImage: string;
}

export interface MainNavbarProps {
  user: User | null;
  cartItems: CartItem[];
}

export async function MainNavbar({ user, cartItems }: MainNavbarProps){

  const categories = await getMegaMenuCategories()

  const accountProps = user ? {
    firstName: user.firstName || '',
    email: user.email || '',
    profileImage: user.profileImage || '',
  } : null;

  return (
    <MainNavbarClient
      accountProps={accountProps}
      cartItems={cartItems}
      categories={categories}
    />
  )
}
