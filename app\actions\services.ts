'use server';

import { revalidatePath } from 'next/cache';
import { getCurrentDbUser } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { prisma, withRetry } from '@/lib/db';
import {
  createServiceRequestSchema,
  updateServiceRequestSchema,
  type CreateServiceRequestInput,
  type UpdateServiceRequestInput,
} from '@/lib/zod';
import { ServiceActionResult, ServiceEmailData } from '@/types/services';
import { ServiceStatus } from '@/generated/prisma';
import { sendServiceStatusEmail } from '@/lib/emailService';
import { headers } from 'next/headers';

export interface ActionResult {
  success: boolean;
  error?: string;
}

/**
 * Generate a unique service number (following return pattern)
 */
async function generateServiceNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `SVC-${year}-`;

  // Get the latest service number for this year
  const latestService = await prisma.serviceRequest.findFirst({
    where: {
      serviceNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      serviceNumber: 'desc',
    },
    select: {
      serviceNumber: true,
    },
  });

  let nextNumber = 1;
  if (latestService) {
    const currentNumber = parseInt(latestService.serviceNumber.split('-')[2]);
    nextNumber = currentNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(5, '0')}`;
}

/**
 * Generate a unique action number for OrderPostPurchaseAction
 */
async function generateActionNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `PPA-${year}-`;

  // Get the latest action number for this year
  const latestAction = await prisma.orderPostPurchaseAction.findFirst({
    where: {
      actionNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      actionNumber: 'desc',
    },
    select: {
      actionNumber: true,
    },
  });

  let nextNumber = 1;
  if (latestAction) {
    const currentNumber = parseInt(latestAction.actionNumber.split('-')[2]);
    nextNumber = currentNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(5, '0')}`;
}

/**
 * Create a new service request (following return pattern)
 */
export async function createServiceRequest(
  input: CreateServiceRequestInput
): Promise<ServiceActionResult> {
  try {
    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Validate input
    const validationResult = createServiceRequestSchema.safeParse(input);
    if (!validationResult.success) {
      const errors = validationResult.error.errors.map(err => err.message).join(', ');
      return { success: false, error: `Date invalide: ${errors}` };
    }

    const validatedData = validationResult.data;

    // Get audit data
    const headerPayload = await headers();
    const auditData = {
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    // Verify order item exists and is eligible for service
    const orderItem = await withRetry(() =>
      prisma.orderItem.findUnique({
        where: {
          id: validatedData.orderItemId,
        },
        include: {
          order: {
            select: {
              id: true,
              userId: true,
              orderStatus: true,
              orderNumber: true,
            },
          },
          product: {
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              isServiceable: true,
            },
          },
          // Check for existing active actions
          action: {
            where: {
              status: 'ACTIVE',
            },
            select: {
              id: true,
              type: true,
              actionNumber: true,
            },
          },
        },
      })
    );

    if (!orderItem) {
      return { success: false, error: 'Produsul specificat nu a fost găsit' };
    }

    // Check if order belongs to user
    if (orderItem.order.userId !== user.id) {
      return { success: false, error: 'Nu aveți permisiunea să creați o cerere de service pentru acest produs' };
    }

    // Check if order is completed (only completed orders can have service requests)
    if (orderItem.order.orderStatus !== 'completa') {
      return { success: false, error: 'Cererea de service poate fi creată doar pentru comenzi finalizate' };
    }

    // Check if there's already an active action for this order item
    if (orderItem.action && orderItem.action.length > 0) {
      const activeAction = orderItem.action[0];
      return {
        success: false,
        error: `Există deja o cerere ${activeAction.type === 'SERVICE' ? 'de service' : 'de retur'} activă pentru acest produs (${activeAction.actionNumber})`,
      };
    }

    // Verify address belongs to user
    const address = await withRetry(() =>
      prisma.shippingAddress.findFirst({
        where: {
          id: validatedData.addressId,
          userId: user.id,
        },
        select: {
          id: true,
        },
      })
    );

    if (!address) {
      return { success: false, error: 'Adresa nu a fost găsită' };
    }

    // Generate service number and action number
    const serviceNumber = await generateServiceNumber();
    const actionNumber = await generateActionNumber();

    // Create OrderPostPurchaseAction and ServiceRequest in a transaction
    const result = await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // Create OrderPostPurchaseAction
        const action = await tx.orderPostPurchaseAction.create({
          data: {
            actionNumber,
            orderItemId: validatedData.orderItemId,
            userId: user.id,
            type: 'SERVICE',
            status: 'ACTIVE',
          },
        });

        // Create ServiceRequest
        const serviceRequest = await tx.serviceRequest.create({
          data: {
            serviceNumber,
            userId: user.id,
            issueType: validatedData.issueType,
            description: validatedData.description,
            method: validatedData.method,
            addressId: validatedData.addressId,
            showroomId: validatedData.showroomId,
            status: ServiceStatus.requested,
            emailsSent: 0,
          },
        });

        // Create audit log
        await tx.userAuditLog.create({
          data: {
            userId: user.id,
            action: 'service.create',
            entityType: 'service',
            entityId: serviceRequest.id,
            details: {
              serviceNumber: serviceRequest.serviceNumber,
              issueType: validatedData.issueType,
              description: validatedData.description,
              method: validatedData.method,
              addressId: validatedData.addressId,
              showroomId: validatedData.showroomId,
              ...auditData,
            },
            ipAddress: auditData.ipAddress,
            userAgent: auditData.userAgent,
          },
        });

        // Link the action to the service request
        await tx.orderPostPurchaseAction.update({
          where: { id: action.id },
          data: { serviceRequestId: serviceRequest.id },
        });

        return { action, serviceRequest };
      })
    );

    // Create initial status history entry
    await withRetry(() =>
      prisma.serviceStatusHistory.create({
        data: {
          serviceRequestId: result.serviceRequest.id,
          newStatus: ServiceStatus.requested,
          notes: 'Cerere de service creată',
          changedBy: user.id,
          emailSent: false,
        },
      })
    );

    // Send email notification
    const emailData: ServiceEmailData = {
      serviceNumber: result.serviceRequest.serviceNumber,
      customerName: `${user.firstName} ${user.lastName}`,
      customerEmail: user.email,
      status: ServiceStatus.requested,
      productName: orderItem.product.Description_Local || 'Produs necunoscut',
      productCode: orderItem.product.Material_Number,
      orderNumber: orderItem.order.orderNumber,
    };

    const emailResult = await sendServiceStatusEmail(emailData);

    if (emailResult.success) {
      // Update email sent status
      await withRetry(() =>
        prisma.serviceRequest.update({
          where: { id: result.serviceRequest.id },
          data: {
            lastEmailSentAt: new Date(),
            emailsSent: 1,
          },
        })
      );

      // Update status history for email sent
      await withRetry(() =>
        prisma.serviceStatusHistory.updateMany({
          where: {
            serviceRequestId: result.serviceRequest.id,
            newStatus: ServiceStatus.requested,
          },
          data: {
            emailSent: true,
          },
        })
      );
    }

    // Log the action
    logger.info(`[createServiceRequest] Service request created: ${result.serviceRequest.serviceNumber} (Action: ${result.action.actionNumber}) by user: ${user.id}`);

    // Revalidate the service page
    revalidatePath('/account/service');

    return {
      success: true,
      serviceRequestId: result.serviceRequest.id,
      serviceNumber: result.serviceRequest.serviceNumber,
      actionNumber: result.action.actionNumber,
    };
  } catch (error) {
    logger.error('[createServiceRequest] Error creating service request:', error);
    return { success: false, error: 'Eroare la crearea cererii de service' };
  }
}

/**
 * Cancel a service request
 */
export async function cancelServiceRequest(serviceRequestId: string): Promise<ActionResult> {
  try {
    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      return { success: false, error: 'Utilizatorul nu este autentificat' };
    }

    // Get audit data
    const headerPayload = await headers();
    const auditData = {
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    // Check if service request exists and belongs to user
    const existingServiceRequest = await withRetry(() =>
      prisma.serviceRequest.findFirst({
        where: {
          id: serviceRequestId,
          userId: user.id,
        },
        include: {
          action: {
            select: {
              id: true,
              actionNumber: true,
              status: true,
            },
          },
        },
      })
    );

    if (!existingServiceRequest) {
      return { success: false, error: 'Cererea de service nu a fost găsită' };
    }

    // Check if service request can be cancelled
    if (existingServiceRequest.status === ServiceStatus.completed ||
        existingServiceRequest.status === ServiceStatus.delivered ||
        existingServiceRequest.status === ServiceStatus.cancelled) {
      return { success: false, error: 'Cererea de service nu poate fi anulată' };
    }

    // Update both service request and action status in a transaction
    await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // Update service request status to cancelled
        await tx.serviceRequest.update({
          where: { id: serviceRequestId },
          data: { status: ServiceStatus.cancelled },
        });

        // Update OrderPostPurchaseAction status to cancelled
        if (existingServiceRequest.action) {
          await tx.orderPostPurchaseAction.update({
            where: { id: existingServiceRequest.action.id },
            data: { status: 'CANCELLED' },
          });
        }
      })
    );

    // Create audit log
    await withRetry(() =>
      prisma.userAuditLog.create({
        data: {
          userId: user.id,
          action: 'service.cancel',
          entityType: 'service',
          entityId: serviceRequestId,
          details: {
            serviceNumber: existingServiceRequest.serviceNumber,
            previousStatus: existingServiceRequest.status,
            ...auditData,
          },
          ipAddress: auditData.ipAddress,
          userAgent: auditData.userAgent,
        },
      })
    );

    // Create status history entry
    await withRetry(() =>
      prisma.serviceStatusHistory.create({
        data: {
          serviceRequestId,
          previousStatus: existingServiceRequest.status,
          newStatus: ServiceStatus.cancelled,
          notes: 'Cerere anulată de client',
          changedBy: user.id,
        },
      })
    );

    // Log the action
    logger.info(`[cancelServiceRequest] Service request cancelled: ${existingServiceRequest.serviceNumber} by user: ${user.id}`);

    // Revalidate the service page
    revalidatePath('/account/service');
    revalidatePath(`/account/service/${serviceRequestId}`);

    return { success: true };
  } catch (error) {
    logger.error('[cancelServiceRequest] Error cancelling service request:', error);
    return { success: false, error: 'Eroare la anularea cererii de service' };
  }
}
