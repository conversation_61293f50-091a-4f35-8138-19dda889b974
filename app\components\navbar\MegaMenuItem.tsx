"use client"

import React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { MegaMenuItemProps } from "@/types/mega-menu"

/**
 * MegaMenuItem - Individual categoryLevel1 trigger component
 * Handles hover detection, underline animation, and active state
 * Triggers the mega-menu dropdown when hovered
 */
export function MegaMenuItem({ 
  category, 
  isActive, 
  onHover, 
  className 
}: MegaMenuItemProps) {
  const handleMouseEnter = () => {
    onHover(category.id)
  }

  const handleMouseLeave = () => {
    // Don't immediately hide - let the parent component handle timing
    // This allows users to move from trigger to dropdown content
  }

  return (
    <div
      className={cn(
        "relative px-4 py-2 cursor-pointer select-none",
        "transition-colors duration-200",
        "hover:text-blue-600 dark:hover:text-blue-400",
        isActive && "text-blue-600 dark:text-blue-400",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Category Name */}
      <span className="text-xs font-medium whitespace-nowrap">
        {category.nameRO || category.name}
      </span>

      {/* Animated Underline */}
      <motion.div
        className="absolute bottom-0 left-4 right-4 h-0.5 bg-blue-600 dark:bg-blue-400"
        initial={{ scaleX: 0 }}
        animate={{ 
          scaleX: isActive ? 1 : 0,
          originX: 0
        }}
        transition={{ 
          duration: 0.2, 
          ease: "easeOut" 
        }}
      />

      {/* Hover Effect Background */}
      <motion.div
        className="absolute inset-0 bg-gray-100 dark:bg-gray-800 rounded-md -z-10"
        initial={{ opacity: 0 }}
        animate={{ opacity: isActive ? 0.1 : 0 }}
        whileHover={{ opacity: 0.05 }}
        transition={{ duration: 0.15 }}
      />
    </div>
  )
}

/**
 * MegaMenuItemSkeleton - Loading state for menu items
 */
export function MegaMenuItemSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("px-4 py-2", className)}>
      <div className="h-4 w-20 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
    </div>
  )
}

/**
 * MegaMenuItemList - Container for multiple menu items
 * Handles spacing and layout for the horizontal menu
 */
export function MegaMenuItemList({ 
  children, 
  className 
}: { 
  children: React.ReactNode
  className?: string 
}) {
  return (
    <div className={cn(
      "flex items-center space-x-2 justify-center",
      "overflow-x-auto scrollbar-hide",
      "py-2",
      className
    )}>
      {children}
    </div>
  )
}
