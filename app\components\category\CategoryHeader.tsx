"use client"

import { useRouter, useSearchParams, usePathname } from "next/navigation"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Filter, X } from "lucide-react"
import { SearchFilters, SearchPageData } from "@/types/search"

interface CategoryHeaderProps {
  appliedFilters: SearchFilters
  totalProducts: number
  categoryName: string
  category3Name?: string
  searchData: SearchPageData
}

export default function CategoryHeader({ appliedFilters, totalProducts, categoryName, category3Name, searchData }: CategoryHeaderProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const pathname = usePathname()

  const removeFilter = (filterType: string, value?: string, attributeKey?: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (filterType === 'category3') {
      params.delete('category3')
      params.delete('category3Id') // Clean up legacy parameter
    } else if (filterType === 'brands' && value) {
      const currentBrands = appliedFilters.brands || []
      const newBrands = currentBrands.filter(brand => brand !== value)
      if (newBrands.length > 0) {
        params.set('brands', newBrands.join(','))
      } else {
        params.delete('brands')
      }
    } else if (filterType === 'classes' && value) {
      const currentClasses = appliedFilters.classes || []
      const newClasses = currentClasses.filter(cls => cls !== value)
      if (newClasses.length > 0) {
        params.set('classes', newClasses.join(','))
      } else {
        params.delete('classes')
      }
    } else if (filterType === 'price') {
      params.delete('minPrice')
      params.delete('maxPrice')
    } else if (filterType === 'hasDiscount') {
      params.delete('hasDiscount')
    } else if (filterType === 'attributes') {
      if (attributeKey && value && appliedFilters.attributes) {
        const currentAttributes = { ...appliedFilters.attributes }
        const currentValues = currentAttributes[attributeKey] || []
        const newValues = currentValues.filter(v => v !== value)

        if (newValues.length === 0) {
          delete currentAttributes[attributeKey]
        } else {
          currentAttributes[attributeKey] = newValues
        }

        // Convert back to URL format
        const attributesString = Object.entries(currentAttributes)
          .map(([k, values]) => `${k}:${values.join(',')}`)
          .join('|')

        if (attributesString) {
          params.set('attributes', attributesString)
        } else {
          params.delete('attributes')
        }
      } else {
        params.delete('attributes')
      }
    }

    params.delete('page') // Reset to first page
    router.push(`${pathname}?${params.toString()}`)
  }

  const handleSortChange = (sort: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (sort === 'relevance') {
      params.delete('sort')
    } else {
      params.set('sort', sort)
    }
    params.delete('page') // Reset to first page when sorting
    router.push(`${pathname}?${params.toString()}`)
  }

  const clearAllFilters = () => {
    const params = new URLSearchParams()
    // Keep only the category route, remove all filters
    router.push(`${pathname}?${params.toString()}`)
  }

  const hasActiveFilters = !!(
    appliedFilters.category3 ||
    appliedFilters.category3Id ||
    appliedFilters.brands?.length ||
    appliedFilters.classes?.length ||
    (appliedFilters.attributes && Object.keys(appliedFilters.attributes).length > 0) ||
    appliedFilters.minPrice ||
    appliedFilters.maxPrice ||
    appliedFilters.hasDiscount
  )

  return (
    <div className="mb-6">
      {/* Category Title and Product Count */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {category3Name ? `${categoryName} - ${category3Name}` : categoryName}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {totalProducts.toLocaleString('ro-RO')} {totalProducts === 1 ? 'produs gasit' : 'produse gasite'}
          </p>
        </div>

        {/* Sort Dropdown */}
            <Select value={appliedFilters.sort || 'relevance'} onValueChange={handleSortChange}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Relevanta</SelectItem>
                {/* <SelectItem value="price_asc">Pret crescator</SelectItem>
                <SelectItem value="price_desc">Pret descrescator</SelectItem>
                <SelectItem value="discount_desc">Reducere mare</SelectItem> */}
                <SelectItem value="name_asc">Nume A-Z</SelectItem>
              </SelectContent>
            </Select>
      </div>

      {/* Active Filters */}
      <div className="flex flex-wrap items-center gap-2">

        {(appliedFilters.category3 || searchData.selectedCategory) && (
          <Badge variant="active" className="flex items-center gap-1">
            Subcategorie: {searchData.selectedCategory?.name || appliedFilters.category3}
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('category3')}
            />
          </Badge>
        )}

        {appliedFilters.brands?.map(brand => (
          <Badge key={brand} variant="active" className="flex items-center gap-1">
            Brand: {brand}
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('brands', brand)}
            />
          </Badge>
        ))}

        {appliedFilters.classes?.map(cls => (
          <Badge key={cls} variant="active" className="flex items-center gap-1">
            Clasa: {cls}
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('classes', cls)}
            />
          </Badge>
        ))}

        {(appliedFilters.minPrice || appliedFilters.maxPrice) && (
          <Badge variant="active" className="flex items-center gap-1">
            Pret: {appliedFilters.minPrice || 0} - {appliedFilters.maxPrice || '∞'} RON
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('price')}
            />
          </Badge>
        )}

        {/* Attribute filters */}
        {appliedFilters.attributes && Object.entries(appliedFilters.attributes).map(([key, values]) =>
          values.map((value) => (
            <Badge key={`${key}-${value}`} variant="active" className="flex items-center gap-1">
              {key}: {value}
              <X
                className="h-4 w-4 cursor-pointer hover:text-red-500"
                onClick={() => removeFilter('attributes', value, key)}
              />
            </Badge>
          ))
        ).flat()}

        {appliedFilters.hasDiscount && (
          <Badge variant="active" className="flex items-center gap-1">
            Cu reducere
            <X
              className="h-4 w-4 cursor-pointer hover:text-red-500"
              onClick={() => removeFilter('hasDiscount')}
            />
          </Badge>
        )}

        {hasActiveFilters && (
          <Badge
            variant="delete"
            onClick={clearAllFilters}
            className="flex items-center gap-1 text-xs cursor-pointer"
          >
            <Filter className="h-4 w-4" />
            Reseteaza filtrele
          </Badge>
        )}
      </div>
    </div>
  )
}
