"use client"

import React, { useState } from "react"
import { CartComponent } from "./Cart"

import SearchSection from "./SearchSection"
import { UserDropdown } from "./UserDropdown"
import { DarkModeButton } from "./DarkModeButton"
import { WishlistComponent } from "../wishlist/WishlistCount"
import { MegaMenu, MegaMenuSkeleton } from "./MegaMenu"
import { MobileNavigation } from "./MobileNavigation"
import { MegaMenuErrorBoundary } from "./MegaMenuErrorBoundary"
import { CartItem } from "@/types/cart"
import { MegaMenuCategory } from "@/types/mega-menu"
import { AccountComponentProps } from "./MainNavbar"
import LogoComponent from "./Logo"

export interface MainNavbarClientProps {
  accountProps: AccountComponentProps | null
  cartItems: CartItem[]
  categories: MegaMenuCategory[]
}

/**
 * MainNavbarClient - Client-side navbar component with mega-menu
 * Handles interactive elements like mobile menu state and mega-menu interactions
 */
export function MainNavbarClient({ accountProps, cartItems, categories }: MainNavbarClientProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false)
  }

  return (
    <>
      {/* Mobile Navigation Overlay */}
      {categories.length > 0 && (
        <MobileNavigation
          categories={categories}
          isOpen={isMobileMenuOpen}
          onClose={handleMobileMenuClose}
        />
      )}

      {/* Desktop Layout */}
      <div className="hidden lg:flex items-center justify-between px-6 py-4 transition-all duration-200">
        {/* Left Section - Logo */}
        <div className="flex items-center">
          <LogoComponent />
        </div>

        {/* Center Section - Search */}
        <div className="flex-1 mx-8">
          <SearchSection />
        </div>

        {/* Right Section - Actions */}
        <div className="flex items-center space-x-4">
          {/* Dark Mode Button */}
          <DarkModeButton />

          {/* Wishlist */}
          <WishlistComponent />

          {/* Cart */}
          <CartComponent cartItems={cartItems} />

          {/* Account */}
          <div className="flex justify-end items-center p-4 gap-4 h-16">
            <UserDropdown accountProps={accountProps} />
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="lg:hidden">
        {/* First Row - Navigation & Actions */}
        <div className="flex items-center justify-between px-4 py-3">
          {/* Left Section - Hamburger + Logo */}
          <div className="flex items-center space-x-3">
            {/* Hamburger Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
              aria-label="Deschide meniul"
            >
              <svg className="w-6 h-6 text-gray-700 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            {/* Logo - Icon Only on Mobile */}
            <LogoComponent />
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center space-x-2">
            {/* Dark Mode Button */}
            <DarkModeButton />

            {/* Wishlist */}
            <WishlistComponent />

            {/* Cart */}
            <CartComponent cartItems={cartItems} />

            {/* Account */}
            <UserDropdown accountProps={accountProps} />
          </div>
        </div>

        {/* Second Row - Search */}
        <div className="px-4 pb-3">
          <SearchSection mobile />
        </div>
      </div>

      {/* Desktop Mega Menu - Below main navbar */}
      <div className="hidden lg:block border-t border-gray-200 dark:border-gray-700">
        <div className="max-w-[1640px] mx-auto">
          <MegaMenuErrorBoundary>
            {categories.length > 0 ? (
              <MegaMenu categories={categories} />
            ) : (
              <MegaMenuSkeleton />
            )}
          </MegaMenuErrorBoundary>
        </div>
      </div>
    </>
  )
}
