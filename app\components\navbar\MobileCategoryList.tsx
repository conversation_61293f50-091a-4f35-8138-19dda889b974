"use client"

import React, { useState } from "react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronDown, ChevronRight } from "lucide-react"

import { MobileCategoryListProps } from "@/types/mega-menu"

/**
 * MobileCategoryList - Mobile-optimized category hierarchy display
 * Features collapsible sections for level1 categories
 * Nested display for level2 and level3 categories
 */
export function MobileCategoryList({ categories, onCategoryClick }: MobileCategoryListProps) {
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set())
  const [expandedLevel2, setExpandedLevel2] = useState<Set<string>>(new Set())

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev)
      if (newSet.has(categoryId)) {
        newSet.delete(categoryId)
      } else {
        newSet.add(categoryId)
      }
      return newSet
    })
  }

  const toggleLevel2 = (level2Id: string) => {
    setExpandedLevel2(prev => {
      const newSet = new Set(prev)
      if (newSet.has(level2Id)) {
        newSet.delete(level2Id)
      } else {
        newSet.add(level2Id)
      }
      return newSet
    })
  }

  const handleLinkClick = (url: string) => {
    if (onCategoryClick) {
      onCategoryClick(url)
    }
  }

  if (!categories || categories.length === 0) {
    return (
      <div className="px-6 py-8 text-center">
        <div className="text-gray-500 dark:text-gray-400">
          Nu sunt categorii disponibile
        </div>
      </div>
    )
  }

  return (
    <div className="py-4">
      {categories.map((category) => (
        <div key={category.id} className="border-b border-gray-100 dark:border-gray-800 last:border-b-0">
          {/* Level 1 Category Header */}
          <button
            onClick={() => toggleCategory(category.id)}
            className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors duration-200"
          >
            <span className="font-semibold text-gray-900 dark:text-gray-100">
              {category.nameRO || category.name}
            </span>
            <motion.div
              animate={{ rotate: expandedCategories.has(category.id) ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </motion.div>
          </button>

          {/* Level 2 Categories */}
          <AnimatePresence>
            {expandedCategories.has(category.id) && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="overflow-hidden"
              >
                <div className="bg-gray-50 dark:bg-gray-800/50">
                  {category.level2Categories.map((level2) => (
                    <div key={level2.id}>
                      {/* Level 2 Header */}
                      <div className="flex items-center">
                        <Link
                          href={`/category/${level2.slug}`}
                          onClick={() => handleLinkClick(`/category/${level2.slug}`)}
                          className="flex-1 px-6 py-3 pl-10 text-sm font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                        >
                          {level2.nameRO || level2.name}
                        </Link>
                        
                        {level2.level3Categories.length > 0 && (
                          <button
                            onClick={() => toggleLevel2(level2.id)}
                            className="px-3 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
                          >
                            <motion.div
                              animate={{ rotate: expandedLevel2.has(level2.id) ? 90 : 0 }}
                              transition={{ duration: 0.2 }}
                            >
                              <ChevronRight className="h-3 w-3 text-gray-400" />
                            </motion.div>
                          </button>
                        )}
                      </div>

                      {/* Level 3 Categories */}
                      <AnimatePresence>
                        {expandedLevel2.has(level2.id) && level2.level3Categories.length > 0 && (
                          <motion.div
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: "auto", opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={{ duration: 0.15, ease: "easeInOut" }}
                            className="overflow-hidden"
                          >
                            <div className="bg-gray-100 dark:bg-gray-700/50">
                              {level2.level3Categories.map((level3) => (
                                <Link
                                  key={level3.id}
                                  href={`/category/${level2.slug}?category3=${level3.slug}`}
                                  onClick={() => handleLinkClick(`/category/${level2.slug}?category3=${level3.slug}`)}
                                  className="block px-6 py-2 pl-16 text-xs text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
                                >
                                  <div className="flex items-center justify-between">
                                    <span className="truncate pr-2">
                                      {level3.nameRO || level3.name}
                                    </span>
                                    {level3.productCount > 0 && (
                                      <span className="text-xs text-gray-400 bg-gray-200 dark:bg-gray-600 px-2 py-0.5 rounded-full flex-shrink-0">
                                        {level3.productCount}
                                      </span>
                                    )}
                                  </div>
                                </Link>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      ))}
    </div>
  )
}

/**
 * MobileCategoryListSkeleton - Loading state for mobile category list
 */
export function MobileCategoryListSkeleton() {
  return (
    <div className="py-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <div key={i} className="border-b border-gray-100 dark:border-gray-800">
          <div className="px-6 py-4 flex items-center justify-between">
            <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
            <div className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
          </div>
        </div>
      ))}
    </div>
  )
}
