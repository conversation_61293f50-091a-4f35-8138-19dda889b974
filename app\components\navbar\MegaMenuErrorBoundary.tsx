"use client"

import React from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, Refresh<PERSON><PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

interface MegaMenuErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface MegaMenuErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

/**
 * MegaMenuErrorBoundary - Error boundary for mega-menu components
 * Provides graceful error handling and recovery options
 */
export class MegaMenuErrorBoundary extends React.Component<
  MegaMenuErrorBoundaryProps,
  MegaMenuErrorBoundaryState
> {
  constructor(props: MegaMenuErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): MegaMenuErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('MegaMenu Error:', error, errorInfo)
    
    // Log to monitoring service if available
    if (typeof window !== 'undefined' && (window as unknown as { gtag?: (...args: unknown[]) => void }).gtag) {
      (window as unknown as { gtag: (...args: unknown[]) => void }).gtag('event', 'exception', {
        description: `MegaMenu Error: ${error.message}`,
        fatal: false
      })
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined })
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultMegaMenuErrorFallback
      return (
        <FallbackComponent 
          error={this.state.error || new Error('Unknown error')} 
          retry={this.handleRetry} 
        />
      )
    }

    return this.props.children
  }
}

/**
 * Default error fallback component for mega-menu
 */
function DefaultMegaMenuErrorFallback({ 
  error, 
  retry 
}: { 
  error: Error
  retry: () => void 
}) {
  return (
    <div className="py-4 px-6">
      <div className="flex items-center space-x-3 text-red-600 dark:text-red-400">
        <AlertTriangle className="h-5 w-5 flex-shrink-0" />
        <div className="flex-1">
          <p className="text-sm font-medium">
            Eroare la încărcarea meniului
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {error.message}
          </p>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={retry}
          className="flex items-center space-x-1 text-xs"
        >
          <RefreshCw className="h-3 w-3" />
          <span>Încearcă din nou</span>
        </Button>
      </div>
    </div>
  )
}

/**
 * Compact error fallback for mobile navigation
 */
export function MobileMegaMenuErrorFallback({
  retry
}: {
  error?: Error
  retry: () => void
}) {
  return (
    <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
          <AlertTriangle className="h-4 w-4" />
          <span className="text-sm">Eroare meniu</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={retry}
          className="text-xs"
        >
          Reîncarcă
        </Button>
      </div>
    </div>
  )
}

/**
 * Hook for handling mega-menu errors in functional components
 */
export function useMegaMenuErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null)

  const handleError = React.useCallback((error: Error) => {
    console.error('MegaMenu Error:', error)
    setError(error)
    
    // Log to monitoring service
    if (typeof window !== 'undefined' && (window as unknown as { gtag?: (...args: unknown[]) => void }).gtag) {
      (window as unknown as { gtag: (...args: unknown[]) => void }).gtag('event', 'exception', {
        description: `MegaMenu Error: ${error.message}`,
        fatal: false
      })
    }
  }, [])

  const clearError = React.useCallback(() => {
    setError(null)
  }, [])

  return { error, handleError, clearError }
}
