"use client"

import { ProductCardInterface } from "@/types/product"
import { ProductCardWithoutEffect } from "../product/ProductCardWithoutEffect"
import { BatchImageResponse } from "@/app/getData/products"

interface SearchResultsProps {
  products: ProductCardInterface[]
  productsImages: BatchImageResponse
}

export default function SearchResults({ products, productsImages }: SearchResultsProps) {
  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 dark:text-gray-600 mb-4">
          <svg
            className="mx-auto h-12 w-12"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
          Nu am gasit produse
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Incearca sa modifici filtrele sau termenii de cautare.
        </p>
      </div>
    )
  }

  return (
    <div className="grid xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
      {products.map((product, index) => (
        <ProductCardWithoutEffect index={index}
          key={product.Material_Number}
          product={product}
          productsImages={productsImages}
        />
      ))}
    </div>
  )
}
