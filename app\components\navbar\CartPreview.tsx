"use client"

import { ShoppingCart } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { CartItem } from "@/types/cart";
import { formatPriceRON } from "@/lib/utils";
import { usePathname } from 'next/navigation';
import CartPreviewItem from "./CartPreviewItem";

const CartPreview = ({ items, cartCount }: { items: CartItem[], cartCount: number }) => {

  const pathname = usePathname();
  
  // Check if we're on the cart page
  if (pathname === '/cart') {
    return null;
  }

  const subtotal = items.reduce(
    (acc, item) => acc + (item.FinalPrice || 0) * item.quantity,
    0,
  );

  if (items.length === 0){
    return (
    <div 
        className="mt-1 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
                bg-white dark:bg-gray-800  group-hover:opacity-100 group-hover:visible 
                  transition-all duration-200
                  w-[55vw] sm:w-[260px] md:w-[320px] lg:w-[380px] max-w-[55vw]"
    >
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Cos</h3>
            <Badge variant="secondary">0 produse</Badge>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">Cosul tau este gol, incearca sa adaugi ceva.</p>
        </div>
      </div>
    )
  };

  return (
    <div 
        className="mt-1 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
                bg-white dark:bg-gray-800  group-hover:opacity-100 group-hover:visible 
                  transition-all duration-200
                  w-[55vw] sm:w-[260px] md:w-[320px] lg:w-[380px] max-w-[55vw]"
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium text-gray-900 dark:text-gray-100">Cos</h3>
          <Badge variant="secondary">{cartCount > 1 ? `${cartCount} produse` : `${cartCount} produs`}</Badge>
        </div>
        
        <div className="space-y-3 max-h-60 overflow-auto">
          {items.map((item) => (
            <CartPreviewItem key={item.id} item={item} />
          ))}
        </div>
        
        <div className="mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
          <div className="flex justify-between mb-4">
            <span className="font-medium text-gray-900 dark:text-gray-100">Subtotal:</span>
            <span className="font-medium text-gray-900 dark:text-gray-100">{formatPriceRON(subtotal)}</span>
          </div>
          
          <div className="space-y-2">
            <Link href="/cart">
              <Button className="w-full bg-[#0066B1] hover:bg-[#004d85] text-white">
                <ShoppingCart className="h-5 w-5 mr-2 " />
                Vezi cos
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CartPreview;


// absolute right-0 top-full mt-1 z-50 border border-gray-100 dark:border-gray-700 rounded-lg shadow-xl
//                 bg-white dark:bg-gray-800 opacity-0 invisible group-hover:opacity-100 group-hover:visible 
//                   transition-all duration-200