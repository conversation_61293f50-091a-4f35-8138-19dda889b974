"server-only"

import { cache } from "react";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { ActiveShowrooms, BillingAddress, ShippingAddress } from "@/types/addresses";

/**
 * Fetches all billing addresses for a user
 */
export const getUserBillingAddresses = cache(async (userId: string): Promise<BillingAddress[]> => {
  try {
    const addresses = await withRetry(() =>
      prisma.billingAddress.findMany({
        where: {
          userId,
        },
        orderBy: [
          { isDefault: 'desc' }, // Default addresses first
          { createdAt: 'desc' }   // Then by creation date
        ],
        select: {
          id: true,
          fullName: true,
          companyName: true,
          address: true,
          city: true,
          county: true,
          cui: true,
          bank: true,
          iban: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert Prisma types to clean interfaces
    return addresses.map(address => ({
      id: address.id,
      fullName: address.fullName,
      companyName: address.companyName || undefined,
      address: address.address,
      city: address.city,
      county: address.county,
      cui: address.cui || undefined,
      bank: address.bank || undefined,
      iban: address.iban || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    }));

  } catch (error) {
    logger.error(`[getUserBillingAddresses] Error fetching billing addresses for user ${userId}:`, error);
    return [];
  }
});

/**
 * Fetches a single billing address by ID for the user
 */
export const getUserBillingAddress = cache(async (userId: string, addressId: string): Promise<BillingAddress | null> => {
  try {
    const address = await withRetry(() =>
      prisma.billingAddress.findFirst({
        where: {
          id: addressId,
          userId,
        },
        select: {
          id: true,
          fullName: true,
          companyName: true,
          address: true,
          city: true,
          county: true,
          cui: true,
          bank: true,
          iban: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    if (!address) {
      logger.warn(`[getUserBillingAddress] Billing address not found: ${addressId} for user: ${userId}`);
      return null;
    }

    return {
      id: address.id,
      fullName: address.fullName,
      companyName: address.companyName || undefined,
      address: address.address,
      city: address.city,
      county: address.county,
      cui: address.cui || undefined,
      bank: address.bank || undefined,
      iban: address.iban || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

  } catch (error) {
    logger.error(`[getUserBillingAddress] Error fetching billing address ${addressId} for user ${userId}:`, error);
    return null;
  }
});

/**
 * Fetches all shipping addresses for a user
 */
export const getUserShippingAddresses = cache(async (userId: string): Promise<ShippingAddress[]> => {
  try {
    const addresses = await withRetry(() =>
      prisma.shippingAddress.findMany({
        where: {
          userId,
        },
        orderBy: [
          { isDefault: 'desc' }, // Default addresses first
          { createdAt: 'desc' }   // Then by creation date
        ],
        select: {
          id: true,
          fullName: true,
          address: true,
          city: true,
          county: true,
          phoneNumber: true,
          notes: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert Prisma types to clean interfaces
    return addresses.map(address => ({
      id: address.id,
      fullName: address.fullName,
      address: address.address,
      city: address.city,
      county: address.county,
      phoneNumber: address.phoneNumber,
      notes: address.notes || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    }));

  } catch (error) {
    logger.error(`[getUserShippingAddresses] Error fetching shipping addresses for user ${userId}:`, error);
    return [];
  }
});

/**
 * Fetches a single shipping address by ID for the user
 */
export const getUserShippingAddress = cache(async (userId: string, addressId: string): Promise<ShippingAddress | null> => {
  try {
    const address = await withRetry(() =>
      prisma.shippingAddress.findFirst({
        where: {
          id: addressId,
          userId,
        },
        select: {
          id: true,
          fullName: true,
          address: true,
          city: true,
          county: true,
          phoneNumber: true,
          notes: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    if (!address) {
      logger.warn(`[getUserShippingAddress] Shipping address not found: ${addressId} for user: ${userId}`);
      return null;
    }

    return {
      id: address.id,
      fullName: address.fullName,
      address: address.address,
      city: address.city,
      county: address.county,
      phoneNumber: address.phoneNumber,
      notes: address.notes || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

  } catch (error) {
    logger.error(`[getUserShippingAddress] Error fetching shipping address ${addressId} for user ${userId}:`, error);
    return null;
  }
});

/**
 * Fetches all active showrooms
 */
export const getActiveShowrooms = cache(async (): Promise<ActiveShowrooms[]> => {
  try {
    const showrooms = await withRetry(() =>
      prisma.showroom.findMany({
        where: {
          isActive: true,
        },
        orderBy: [
          { sortOrder: 'asc' }, // Custom sort order first
          { name: 'asc' }       // Then alphabetically
        ],
        select: {
          id: true,
          code: true,
          name: true,
          address1: true,
          address2: true,
          city: true,
          county: true,
          postalCode: true,
          phone: true,
          email: true,
          program: true,
          isActive: true,
          sortOrder: true,
        }
      })
    );

    logger.info(`[getActiveShowrooms] Found ${showrooms.length} active showrooms`);
    return showrooms;

  } catch (error) {
    logger.error(`[getActiveShowrooms] Error fetching showrooms:`, error);
    return [];
  }
});
