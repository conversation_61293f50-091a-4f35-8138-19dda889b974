{"name": "pieseabv-frontend", "version": "0.1.0", "private": true, "scripts": {"build": "prisma generate && next build", "dev": "next dev", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio"}, "dependencies": {"@clerk/elements": "^0.23.37", "@clerk/nextjs": "^6.21.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@upstash/redis": "^1.35.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "framer-motion": "^12.16.0", "ioredis": "^5.7.0", "lucide-react": "^0.513.0", "mssql": "^11.0.1", "next": "15.3.3", "next-themes": "^0.4.6", "nodemailer": "^7.0.4", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.59.0", "react-icons": "^5.5.0", "sonner": "^2.0.6", "svix": "^1.67.0", "tailwind-merge": "^3.3.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.25.63"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/mssql": "^9.1.7", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.11.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5"}}