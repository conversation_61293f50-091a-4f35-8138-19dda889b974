"use client"

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Eye,
  Download,
  ChevronLeft,
  ChevronRight,
  Search,
  Filter,
  Truck,
  Package,
  CheckCircle,
  Clock,
} from "lucide-react";
import { cn, formatPriceRON } from "@/lib/utils";
import { useState, useTransition, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import OrderDetails from "./OrderDetails";
import { Order, OrderFilters } from "@/types/orders";
import {
  getOrderStatusBadgeColor,
  formatCurrency,
  formatDate,
  generateMockTrackingHistory
} from "@/lib/order-utils";
import { generateInvoiceDownloadToken } from "@/app/actions/invoices";
import { toast } from "sonner";
//import { OrderStatus } from "@/generated/prisma";
import { OrderStatus as PrismaOrderStatus } from '@/generated/prisma';

interface OrdersPageProps {
  initialOrders: Order[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: OrderFilters;
}

const isValidOrderStatus = (value: string): value is PrismaOrderStatus | 'all' => {
  return value === 'all' || Object.values(PrismaOrderStatus).includes(value as PrismaOrderStatus);
};

const OrdersPage = ({ initialOrders, pagination, filters }: OrdersPageProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();

  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showShippingOnly, setShowShippingOnly] = useState(false);
  const [orderType, setOrderType] = useState<string>(filters.status || "all");
  const [searchQuery, setSearchQuery] = useState<string>(filters.search || "");
  const [dateFrom, setDateFrom] = useState<string>(filters.dateFrom || "");
  const [dateTo, setDateTo] = useState<string>(filters.dateTo || "");

  // Update URL with new search params
  const updateSearchParams = useCallback((newParams: Partial<OrderFilters>) => {
    const params = new URLSearchParams(searchParams.toString());

    Object.entries(newParams).forEach(([key, value]) => {
      if (value && value !== "all" && value !== "") {
        params.set(key, value.toString());
      } else {
        params.delete(key);
      }
    });

    // Reset to page 1 when filters change (except when changing page)
    if (!newParams.page) {
      params.delete("page");
    }

    startTransition(() => {
      router.push(`/account/orders?${params.toString()}`);
    });
  }, [router, searchParams, startTransition]);

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    updateSearchParams({ search: value });
  };

  // Handle order type filter change
  const handleOrderTypeChange = (value: string) => {
    // setOrderType(value);
    // updateSearchParams({ status: value });
    if (isValidOrderStatus(value)) {
      setOrderType(value);
      updateSearchParams({ status: value });
    } else {
      // Handle invalid status - maybe log a warning or use default
      console.warn(`Invalid order status: ${value}`);
      setOrderType('all');
      updateSearchParams({ status: 'all' });
    }
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    updateSearchParams({ page });
  };

  const handleDateFromChange = (value: string) => {
    setDateFrom(value);
    updateSearchParams({ dateFrom: value });
  };

  const handleDateToChange = (value: string) => {
    setDateTo(value);
    updateSearchParams({ dateTo: value });
  };

  const getShippingStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
      case "complete":
        return <CheckCircle className="w-4 h-4" />;
      case "shipped":
      case "in transit":
        return <Truck className="w-4 h-4" />;
      case "processing":
      case "confirmed":
      case "prepared":
        return <Package className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getShippingStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
      case "complete":
        return "text-green-600 hover:text-green-700";
      case "shipped":
      case "in transit":
        return "text-blue-600 hover:text-blue-700";
      case "processing":
      case "confirmed":
      case "prepared":
        return "text-orange-600 hover:text-orange-700";
      default:
        return "text-gray-600 hover:text-gray-700";
    }
  };

  const handleShippingStatus = (order: Order) => {
    // Add mock tracking history for demonstration
    const orderWithTracking = {
      ...order,
      shippingHistory: generateMockTrackingHistory(order.orderStatus, order.shipmentStatus, order.placedAt)
    };
    setSelectedOrder(orderWithTracking);
    setShowShippingOnly(true);
  };

  const handleViewDetails = (order: Order) => {
    // Add mock tracking history for demonstration
    const orderWithTracking = {
      ...order,
      shippingHistory: generateMockTrackingHistory(order.orderStatus, order.shipmentStatus, order.placedAt)
    };
    setSelectedOrder(orderWithTracking);
    setShowShippingOnly(false);
  };

  const handleDownloadInvoice = async (order: Order) => {
    if (!order.invoiceAM) {
      toast.error("Nu există factură disponibilă pentru această comandă");
      return;
    }

    try {
      startTransition(async () => {
        const result = await generateInvoiceDownloadToken({ orderId: order.id });

        if (result.success && result.downloadUrl) {
          // Open download URL in new window/tab
          window.open(result.downloadUrl, '_blank');
          toast.success("Descărcarea facturii a început");
        } else {
          toast.error(result.error || "Eroare la descărcarea facturii");
        }
      });
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast.error("Eroare la descărcarea facturii");
    }
  };
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg text-center">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Comenzile mele</h1>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Cauta comenzi dupa ID, nume produs sau cod OE..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onBlur={(e) => handleSearchChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSearchChange(e.currentTarget.value);
              }
            }}
            className="pl-10"
            disabled={isPending}
          />
        </div>
        <div className="flex gap-2">
          <Input
            type="date"
            value={dateFrom}
            onChange={(e) => setDateFrom(e.target.value)}
            onBlur={(e) => handleDateFromChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleDateFromChange(e.currentTarget.value);
              }
            }}
            //placeholder="De la"
            //className="w-48"
            disabled={isPending}
          />
          <Input
            type="date"
            value={dateTo}
            onChange={(e) => setDateTo(e.target.value)}
            onBlur={(e) => handleDateToChange(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleDateToChange(e.currentTarget.value);
              }
            }}
            //placeholder="Pana la"
            //className="w-48"
            disabled={isPending}
          />
        </div>
        <div className="flex gap-2">
          <Select value={orderType} onValueChange={handleOrderTypeChange} disabled={isPending}>
            <SelectTrigger className="w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Order Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toate comenzile</SelectItem>
              <SelectItem value="plasata">Deschise</SelectItem>
              <SelectItem value="completa">Inchise</SelectItem>
              <SelectItem value="anulata">Anulate</SelectItem>

            </SelectContent>
          </Select>
        </div>
        
        {(filters.search || filters.status !== "all" || filters.dateFrom || filters.dateTo) && (
          <Button
            variant="outline"
            onClick={() => {
              setSearchQuery("");
              setOrderType("all");
              setDateFrom("");
              setDateTo("");
              updateSearchParams({ search: "", status: "all", dateFrom: "", dateTo: "" });
            }}
          >
            Sterge filtre
          </Button>
        )}

      </div>

      <div className="space-y-4">
        {isPending && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0066B1]"></div>
            <span className="ml-2 text-gray-600">Se incarca...</span>
          </div>
        )}

        {!isPending && initialOrders.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Nici o comanda gasita</h3>
            <p className="text-gray-500 mb-4">
              {filters.search || filters.status !== "all"
                ? "Incercati sa ajustati cautarea sau filtrele pentru a gasi ceea ce cautati."
                : "Nu ati plasat inca nicio comanda. Incepeti cumparaturile pentru a va vedea comenzile aici."
              }
            </p>
            {(filters.search || filters.status !== "all") && (
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery("");
                  setOrderType("all");
                  setDateFrom("");
                  setDateTo("");
                  updateSearchParams({ search: "", status: "all", dateFrom: "", dateTo: "" });
                }}
              >
                Sterge filtre
              </Button>
            )}
          </div>
        )}

        {!isPending && initialOrders.map((order) => (
          <div
            key={order.id}
            className="border rounded-lg shadow p-6 hover:border-[#0066B1] transition-colors"
          >
            <div className="flex flex-wrap gap-4 justify-between items-start mb-3">
              <div>
                <h3 className="font-medium text-base">{order.orderNumber}</h3>
                <p className="text-xs text-gray-500">
                  {formatDate(order.date)}
                </p>
              </div>
              <Badge
                className={cn(getOrderStatusBadgeColor(order.orderStatus))}
              >
                {order.status}
              </Badge>
            </div>

            <div className="space-y-2 mb-3">
              {order.items.slice(0, 2).map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-sm">{item.name}</p>
                    <p className="text-xs text-gray-500">
                      Cant: {item.quantity} • {formatCurrency(item.price)}
                    </p>
                  </div>
                </div>
              ))}
              {order.items.length > 2 && (
                <p className="text-xs text-gray-500">
                  +{order.items.length - 2} mai multe produse
                </p>
              )}
            </div>

            <div className="pt-3 border-t border-gray-100 flex flex-wrap items-center justify-between gap-2">
              <div className="font-semibold text-base">
                {formatPriceRON(order.totalAmount)}
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-xs px-2 py-1 h-7"
                  onClick={() => handleViewDetails(order)}
                >
                  <Eye className="w-3 h-3" /> Detalii
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-xs px-2 py-1 h-7"
                  onClick={() => handleDownloadInvoice(order)}
                  disabled={!order.invoiceAM || isPending}
                  title={!order.invoiceAM ? "Nu există factură disponibilă" : "Descarcă factura"}
                >
                  <Download className="w-3 h-3" /> Factura
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className={`gap-1 text-xs px-2 py-1 h-7 ${getShippingStatusColor(order.status)}`}
                  onClick={() => handleShippingStatus(order)}
                  title={`Shipping Status: ${order.status}`}
                >
                  {getShippingStatusIcon(order.status)} 
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          open={!!selectedOrder}
          onOpenChange={(open) => !open && setSelectedOrder(null)}
          showShippingOnly={showShippingOnly}
        />
      )}

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="mt-8 flex justify-center">
          <nav className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasPrev || isPending}
              onClick={() => handlePageChange(pagination.currentPage - 1)}
            >
              <ChevronLeft className="h-4 w-4 mr-1" /> Pagina anterioara
            </Button>

            {/* Page numbers */}
            {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
              const pageNum = Math.max(1, pagination.currentPage - 2) + i;
              if (pageNum > pagination.pages) return null;

              return (
                <Button
                  key={pageNum}
                  variant="outline"
                  size="sm"
                  className={cn(
                    "min-w-[2.5rem]",
                    pageNum === pagination.currentPage && "bg-[#0066B1] text-white hover:bg-[#004d85]"
                  )}
                  onClick={() => handlePageChange(pageNum)}
                  disabled={isPending}
                >
                  {pageNum}
                </Button>
              );
            })}

            <Button
              variant="outline"
              size="sm"
              disabled={!pagination.hasNext || isPending}
              onClick={() => handlePageChange(pagination.currentPage + 1)}
            >
              Pagina urmatoare <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </nav>
        </div>
      )}

      {/* Results info */}
      <div className="mt-4 text-center text-sm text-gray-500">
        Se afiseaza {initialOrders.length} din {pagination.total} comenzi
        {pagination.pages > 1 && ` (Pagina ${pagination.currentPage} din ${pagination.pages})`}
      </div>
    </div>
  );
};

export default OrdersPage;










