import type {
  ServiceStatus as PrismaServiceStatus,
  ResolutionType as PrismaResolutionType,
  IssueType as PrismaIssueType,
  Prisma
} from "@/generated/prisma";

// Client-safe enum types (for use in client components)
export const ServiceStatus = {
  requested: 'requested',
  scheduled: 'scheduled',
  inProgress: 'inProgress',
  diagnosisComplete: 'diagnosisComplete',
  awaitingParts: 'awaitingParts',
  awaitingApproval: 'awaitingApproval',
  completed: 'completed',
  cancelled: 'cancelled',
  delivered: 'delivered'
} as const;

export type ServiceStatus = typeof ServiceStatus[keyof typeof ServiceStatus];

export const IssueType = {
  DEFECTIVE_PART: 'DEFECTIVE_PART',
  DAMAGED_IN_SHIPPING: 'DAMAGED_IN_SHIPPING',
  WRONG_ITEM_RECEIVED: 'WRONG_ITEM_RECEIVED',
  MISSING_PARTS: 'MISSING_PARTS',
  NOT_AS_DESCRIBED: 'NOT_AS_DESCRIBED',
  COMPATIBILITY_ISSUE: 'COMPATIBILITY_ISSUE',
  OTHER: 'OTHER'
} as const;

export type IssueType = typeof IssueType[keyof typeof IssueType];

export const ResolutionType = {
  REPLACEMENT: 'REPLACEMENT',
  REFUND: 'REFUND',
  STORE_CREDIT: 'STORE_CREDIT',
  REPAIR: 'REPAIR',
  PARTIAL_REFUND: 'PARTIAL_REFUND',
  NO_ACTION_NEEDED: 'NO_ACTION_NEEDED'
} as const;

export type ResolutionType = typeof ResolutionType[keyof typeof ResolutionType];

// OrderPostPurchaseAction types (new unified action system)
export const ActionType = {
  RETURN: 'RETURN',
  SERVICE: 'SERVICE'
} as const;

export type ActionType = typeof ActionType[keyof typeof ActionType];

export const ActionStatus = {
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
} as const;

export type ActionStatus = typeof ActionStatus[keyof typeof ActionStatus];

// OrderPostPurchaseAction Interface
export interface OrderPostPurchaseAction {
  id: string;
  actionNumber: string;
  orderItemId: string;
  userId: string;
  type: ActionType;
  status: ActionStatus;
  returnId?: string;
  serviceRequestId?: string;
  createdAt: string;
  updatedAt: string;
}

// Service Request Interface for UI (updated for new schema with OrderPostPurchaseAction)
export interface ServiceRequest {
  id: string;
  serviceNumber: string;

  // Customer info
  userId: string;

  // Related through OrderPostPurchaseAction
  actionNumber?: string; // From OrderPostPurchaseAction
  orderItemId?: string; // From OrderPostPurchaseAction.orderItem
  orderId?: string; // From OrderPostPurchaseAction.orderItem.order
  orderNumber?: string; // From OrderPostPurchaseAction.orderItem.order
  productName?: string; // From OrderPostPurchaseAction.orderItem.product
  productCode?: string; // From OrderPostPurchaseAction.orderItem.product
  productImage?: string[]; // From OrderPostPurchaseAction.orderItem.product

  // Shipping information
  method: 'curier' | 'intern' | 'showroom';
  addressId: string | null;
  address: {
    id: string;
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string;
  } | null;
  showroomId?: string | null;
  showroom?: {
    id: string;
    code: string;
    name: string;
    address1: string;
    address2?: string;
    city: string;
    county: string;
    phone: string;
    email?: string;
    program?: string;
  } | null;

  // Issue details
  issueType: IssueType;
  description: string;

  // Status tracking
  status: ServiceStatus;

  // Resolution details
  resolution?: ResolutionType;
  resolutionNotes?: string;
  resolvedAt?: string;

  // Communication
  lastEmailSentAt?: string;
  emailsSent: number | null;

  // Audit fields
  createdAt: string;
  updatedAt: string;

  // Status history
  statusHistory: ServiceStatusHistory[];
}

// Service Status History Interface
export interface ServiceStatusHistory {
  id: string;
  serviceRequestId: string;
  previousStatus?: PrismaServiceStatus;
  newStatus: PrismaServiceStatus;
  notes?: string;
  changedBy: string;
  changedAt: string;
  emailSent: boolean;
}

// Raw Service Request from Prisma (for data fetching - updated for new schema)
export interface RawServiceRequestFromPrisma {
  id: string;
  serviceNumber: string;
  userId: string;
  issueType: PrismaIssueType;
  description: string;
  method: 'curier' | 'intern' | 'showroom';
  addressId: string | null;
  showroomId?: string | null;
  status: PrismaServiceStatus;
  resolution: PrismaResolutionType | null;
  resolutionNotes: string | null;
  resolvedAt: Date | null;
  lastEmailSentAt: Date | null;
  emailsSent: number | null;
  createdAt: Date;
  updatedAt: Date;

  // Relations through OrderPostPurchaseAction
  action?: {
    id: string;
    actionNumber: string;
    orderItemId: string;
    orderItem: {
      id: string;
      orderId: string;
      order: {
        id: string;
        orderNumber: string;
      };
      product: {
        id: string;
        Material_Number: string;
        Description_Local: string | null;
        ImageUrl: string[];
      };
    };
  } | null;

  address: {
    id: string;
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string | null;
  } | null;

  showroom?: {
    id: string;
    code: string;
    name: string;
    address1: string;
    address2?: string | null;
    city: string;
    county: string;
    phone: string;
    email?: string | null;
    program?: string | null;
  } | null;
  statusHistory: {
    id: string;
    previousStatus: PrismaServiceStatus | null;
    newStatus: PrismaServiceStatus;
    notes: string | null;
    changedBy: string;
    changedAt: Date;
    emailSent: boolean;
  }[];
}

// Service Request select for Prisma queries (updated for new schema)
export const serviceRequestSelect = {
  id: true,
  serviceNumber: true,
  userId: true,
  issueType: true,
  description: true,
  method: true,
  addressId: true,
  showroomId: true,
  status: true,
  resolution: true,
  resolutionNotes: true,
  resolvedAt: true,
  lastEmailSentAt: true,
  emailsSent: true,
  createdAt: true,
  updatedAt: true,
  action: {
    select: {
      id: true,
      actionNumber: true,
      orderItemId: true,
      orderItem: {
        select: {
          id: true,
          orderId: true,
          order: {
            select: {
              id: true,
              orderNumber: true,
            },
          },
          product: {
            select: {
              id: true,
              Material_Number: true,
              Description_Local: true,
              ImageUrl: true,
            },
          },
        },
      },
    },
  },
  address: {
    select: {
      id: true,
      fullName: true,
      address: true,
      city: true,
      county: true,
      phoneNumber: true,
      notes: true,
    },
  },
  showroom: {
    select: {
      id: true,
      code: true,
      name: true,
      address1: true,
      address2: true,
      city: true,
      county: true,
      phone: true,
      email: true,
      program: true,
    },
  },
  statusHistory: {
    select: {
      id: true,
      previousStatus: true,
      newStatus: true,
      notes: true,
      changedBy: true,
      changedAt: true,
      emailSent: true,
    },
    orderBy: {
      changedAt: 'desc' as const,
    },
  },
} as const satisfies Prisma.ServiceRequestSelect;

// Service Request List Response Interface (following return pattern)
export interface ServiceRequestsResponse {
  serviceRequests: ServiceRequest[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Service Request Filters Interface (following return pattern)
export interface ServiceFilters {
  status?: PrismaServiceStatus | 'all';
  search?: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

// Create Service Request Interface (following return pattern)
export interface CreateServiceRequest {
  orderId: string;
  orderItemId: string;
  issueType: PrismaIssueType;
  description: string;
  addressId: string;
}

// Update Service Request Interface
export interface UpdateServiceRequest {
  id: string;
  status?: PrismaServiceStatus;
  resolution?: PrismaResolutionType;
  resolutionNotes?: string;
}

// Service Statistics Interface (following return pattern)
export interface ServiceStatistics {
  total: number;
  requested: number;
  approved: number;
  rejected: number;
  awaitingReceipt: number;
  received: number;
  processing: number;
  completed: number;
  cancelled: number;
}

// Service Status Display Interface
export interface ServiceStatusDisplay {
  status: PrismaServiceStatus;
  label: string;
  color: 'default' | 'secondary' | 'destructive' | 'outline';
  description: string;
  icon: string;
}

// Service Action Result Interface
export interface ServiceActionResult {
  success: boolean;
  error?: string;
  serviceRequestId?: string;
  serviceNumber?: string;
  actionNumber?: string; // Added for OrderPostPurchaseAction
}

// Product for Service Selection Interface
export interface ProductForService {
  orderItemId: string; // This is what we need for the service request
  productId: string;
  materialNumber: string;
  description: string;
  image: string[];
  orderId: string;
  orderNumber: string;
  orderDate: string;
  quantity: number;
  isServiceable: boolean;
}

// Service Email Data Interface
export interface ServiceEmailData {
  serviceNumber: string;
  customerName: string;
  customerEmail: string;
  status: PrismaServiceStatus;
  productName: string;
  productCode: string;
  orderNumber: string;
  resolution?: PrismaResolutionType;
  resolutionNotes?: string;
}
