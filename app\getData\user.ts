"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils"
import { cuidSchema, userIdClerkSchema } from "@/lib/zod"
import { RawOrderFromPrisma, RecentOrder } from "@/types/product"


export async function getUserIdFromDB(clerId: string){
  if (!clerId){
    logger.warn(`[getUserIdFromDB] No paramater provided`)
    return null
  }

  try{
    //const userIdParsed = cuidSchema.safeParse(userIdDb)
    const parse = userIdClerkSchema.safeParse(clerId)

    if (!parse.success) {
      logger.warn("[getUserIdFromDB] Invalid user ID format")
      return null
    }

    const userId = parse.data

    const userDB = await withRetry(() =>
          prisma.user.findUnique({
            where: { externalId: userId },
            select: {id: true}
          })
        );

    if(!userDB) return null

    return userDB

  }catch(e){
    logger.error(`[getUserIdFromDB] Error trying to get the wishlist count for clerId ${clerId}: ${e}`)
    return null
  }

}




