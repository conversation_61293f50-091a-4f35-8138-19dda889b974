import { redirect } from "next/navigation"

import { SearchFilters } from "@/types/search"
import { getCategoriesPageData } from "@/app/getData/search"
import CategoryPageContent from "@/app/components/category/CategoryPageContent"
import { getCurrentDbUser } from "@/lib/auth"
import { getPretSiStocBatch, getPricesFor4thBatch } from "@/lib/mssql/query"
import { ProductCardInterface } from "@/types/product"
import { logger } from "@/lib/logger"
import { GetPrice4LvlBatch } from "@/types/mssql"
import { getCategoryLevel2BySlug, getCategoryLevel3BySlug } from "@/app/getData/categories"
import { getPicturesForProducts } from "@/app/getData/products"

interface CategoryPageProps {
  params: Promise<{
    category2: string
  }>
  searchParams: Promise<{
    category3?: string 
    brands?: string | string[]
    classes?: string | string[]
    attributes?: string | string[]
    minPrice?: string
    maxPrice?: string
    hasDiscount?: string
    page?: string
    sort?: 'price_asc' | 'price_desc' | 'discount_desc' | 'name_asc' | 'relevance'
  }>
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  try{
    // Await the params and searchParams as required by Next.js 15
    const resolvedParams = await params
    const resolvedSearchParams = await searchParams
    // 1️⃣ Auth
    const user = await getCurrentDbUser()
    if (!user) {
      return redirect("/sign-in")
    }

    // Decode the category2 slug parameter (in case it has URL encoding)
    const category2Slug = decodeURIComponent(resolvedParams.category2)
    const category3Slug = resolvedSearchParams.category3 ? decodeURIComponent(resolvedSearchParams.category3) : undefined
    // Resolve category information by slug
    const category2Info = await getCategoryLevel2BySlug(category2Slug)
    if (!category2Info) {
      logger.error(`[CategoryPage] Category2 not found for slug: ${category2Slug}`)
      return redirect("/")
    }

    let category3Info = null
    let specificCategory3Id: string | undefined = undefined

    if (category3Slug) {
      category3Info = await getCategoryLevel3BySlug(category2Slug, category3Slug)
      if (category3Info) {
        specificCategory3Id = category3Info.id
      } else {
        logger.warn(`[CategoryPage] Category3 not found for slug: ${category2Slug}/${category3Slug}`)
      }
    }

    // Prepare display names using nameRO when available
    const category2DisplayName = category2Info.nameRO || category2Info.name
    const category3DisplayName = category3Info ? (category3Info.nameRO || category3Info.name) : undefined

    // Parse attributes from URL parameters (format: key1:value1,value2|key2:value3)
    const parseAttributes = (attributesParam?: string | string[]): Record<string, string[]> => {
      if (!attributesParam) return {}

      const attributesString = Array.isArray(attributesParam) ? attributesParam.join('|') : attributesParam
      const attributes: Record<string, string[]> = {}

      attributesString.split('|').forEach(keyValuePair => {
        const [key, valuesString] = keyValuePair.split(':')
        if (key && valuesString) {
          attributes[key] = valuesString.split(',').filter(Boolean)
        }
      })

      return attributes
    }

    // Parse search params into filters using slug-based resolution
    const filters: SearchFilters = {
      category2: category2Slug, // Use slug for internal processing
      category3: category3Slug, // Use slug for internal processing
      category3Id: specificCategory3Id, // Use resolved ID for database queries (internal only)
      brands: Array.isArray(resolvedSearchParams.brands)
        ? resolvedSearchParams.brands
        : resolvedSearchParams.brands?.split(',').filter(Boolean) || [],
      classes: Array.isArray(resolvedSearchParams.classes)
        ? resolvedSearchParams.classes
        : resolvedSearchParams.classes?.split(',').filter(Boolean) || [],
      attributes: parseAttributes(resolvedSearchParams.attributes),
      minPrice: resolvedSearchParams.minPrice ? parseFloat(resolvedSearchParams.minPrice) : undefined,
      maxPrice: resolvedSearchParams.maxPrice ? parseFloat(resolvedSearchParams.maxPrice) : undefined,
      hasDiscount: resolvedSearchParams.hasDiscount === 'true',
      page: resolvedSearchParams.page ? parseInt(resolvedSearchParams.page) : 1,
      sort: resolvedSearchParams.sort || 'relevance'
    }

    // 2️⃣ Get category data
    const searchData = await getCategoriesPageData(filters)

    // 3️⃣ Determine if user gets special 4️⃣-level pricing
    const has4th = user.role.includes("fourLvlAdminAB") || user.role.includes("fourLvlInregistratAB")

    // 4️⃣ Prepare OE Code list from products
    const materialNumber = searchData.products.map((p: ProductCardInterface) => p.Material_Number)

    // 5️⃣ Batch-load stock & conditional 4th-level pricing
    const [stockMap, price4Batch, productsImages] = await Promise.all([
      getPretSiStocBatch(materialNumber),
      has4th ? getPricesFor4thBatch(materialNumber, user.userAM || "") : Promise.resolve([]),
      getPicturesForProducts(materialNumber)
    ])

    // 6️⃣ Build price4Map for easy lookup
    const price4Map = new Map(price4Batch.map((p: GetPrice4LvlBatch) => [p.itemno, p.pret]))

    // 7️⃣ Merge everything into enriched products
    const enrichedProducts: ProductCardInterface[] = searchData.products.map((product: ProductCardInterface) => {
      const code = product.Material_Number
      const batch = stockMap[code] ?? []

      // sum stock across all locations
      const stock = batch.reduce((sum, e) => sum + e.stoc, 0)

      // if user has 4th-level role and price exists, use it; else fall back
      const displayPrice = price4Map.get(code) ?? product.FinalPrice

      return {
        ...product,
        stock,
        displayPrice,
      }
    })

    // 8️⃣ Adjust price range for role-based pricing if needed
    let adjustedPriceRange = searchData.priceRange

    if (has4th && price4Batch.length > 0) {
      // If user has 4th level pricing, we need to adjust the range
      // For simplicity, we'll use the database range but apply a general adjustment
      // This is an approximation since we can't easily get all 4th level prices
      const price4Values = price4Batch.map((p: GetPrice4LvlBatch) => p.pret)
      const dbPrices = searchData.products.map((p: ProductCardInterface) => p.FinalPrice).filter((p: number | null) => p !== null) as number[]

      if (price4Values.length > 0 && dbPrices.length > 0) {
        // Calculate average discount ratio from 4th level pricing
        const avgRatio = price4Values.reduce((sum: number, p4Price: number, index: number) => {
          const dbPrice = dbPrices[index]
          return sum + (dbPrice ? p4Price / dbPrice : 1)
        }, 0) / price4Values.length

        adjustedPriceRange = {
          min: Math.round(searchData.priceRange.min * avgRatio * 100) / 100,
          max: Math.round(searchData.priceRange.max * avgRatio * 100) / 100
        }
      }
    }

    // 9️⃣ Update search data with enriched products and adjusted price range
    const enrichedSearchData = {
      ...searchData,
      products: enrichedProducts,
      priceRange: adjustedPriceRange
    }

    return (
      <div className="min-h-screen">
        <div className="max-w-[1640px] mx-auto px-4 py-6">
          <CategoryPageContent
            has4th={has4th}
            searchData={enrichedSearchData}
            categoryName={category2DisplayName}
            category3Name={category3DisplayName}
            productsImages={productsImages}
          />
        </div>
      </div>
    ) 
  }catch(e){
    logger.error(`[CategoryPage] Error fetching category page: ${e}`);

    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <div className="text-center py-12">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Nu s-au putut incarca produsele.
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            A aparut o problema la incarcarea produselor. Reincarca pagina sau incearca din nou mai tarziu.
          </p>
        </div>
      </div>
    )
  }
}
