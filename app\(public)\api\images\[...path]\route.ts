import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import fsp from "fs/promises";
import path from "path";
import { logger } from "@/lib/logger";
import { findProductImages } from "@/app/getData/products";

// Ensure Node runtime (fs isn't available on edge)
export const runtime = "nodejs";

// --- Config ---
const BASE_DIR = process.env.PICTURES_BASE_PATH; // e.g. \\NAS\pics or /mnt/pics
const ALLOWED_EXT = new Set([".jpg", ".jpeg", ".png", ".webp", ".gif", ".avif", ".bmp", ".mp4"]);
const MAX_FILE_BYTES = 50 * 1024 * 1024; // 50MB guard; tune for your needs
const MAX_SEGMENT_LEN = 100; // avoid pathological segments

// Helpers
const isSafeSegment = (seg: string) =>
  seg.length > 0 &&
  seg.length <= MAX_SEGMENT_LEN &&
  !seg.includes("\0") &&
  seg !== "." &&
  seg !== "..";

const makeEtag = (size: number, mtimeMs: number) =>
  `"${size.toString(16)}-${Math.trunc(mtimeMs).toString(16)}"`; // strong enough for static files

function contentTypeFromExt(ext: string): string {
  switch (ext) {
    case ".jpg":
    case ".jpeg":
      return "image/jpeg";
    case ".png":
      return "image/png";
    case ".webp":
      return "image/webp";
    case ".gif":
      return "image/gif";
    case ".avif":
      return "image/avif";
    case ".bmp":
      return "image/bmp";
    case ".mp4":
      return "video/mp4";
    default:
      return "application/octet-stream";
  }
}

function securityHeaders() {
  return {
    "X-Content-Type-Options": "nosniff",
    "Cross-Origin-Resource-Policy": "same-origin",
    "Cross-Origin-Embedder-Policy": "require-corp",
  };
}

// Build absolute, symlink-resolved path and ensure it's inside BASE_DIR
async function safeResolveInsideBase(relativePath: string) {
  if (!BASE_DIR) {
    throw new Error("PICTURES_BASE_PATH is not defined");
  }

  const baseReal = await fsp.realpath(BASE_DIR);
  const target = path.join(BASE_DIR, relativePath);
  const targetReal = await fsp.realpath(target).catch(() => null);
  if (!targetReal || !targetReal.startsWith(baseReal + path.sep)) return null;
  return targetReal;
}

async function statOrNull(p: string) {
  try {
    return await fsp.stat(p);
  } catch {
    return null;
  }
}

function parseRange(rangeHeader: string | null, size: number) {
  if (!rangeHeader || !rangeHeader.startsWith("bytes=")) return null;
  const [startStr, endStr] = rangeHeader.replace("bytes=", "").split("-", 2);
  let start = startStr ? Number(startStr) : NaN;
  let end = endStr ? Number(endStr) : NaN;

  if (Number.isNaN(start)) {
    const last = Number(endStr);
    if (Number.isNaN(last)) return null;
    start = Math.max(size - last, 0);
    end = size - 1;
  } else {
    end = Number.isNaN(end) ? size - 1 : Math.min(end, size - 1);
  }

  if (start < 0 || end < start || start >= size) return null;
  return { start, end };
}

function withCommonHeaders(
  init: ResponseInit,
  cacheSeconds = 60*1 //31536000 // 1 year
): ResponseInit {
  return {
    ...init,
    headers: {
      "Cache-Control": `public, max-age=${cacheSeconds}, immutable`,
      ...securityHeaders(),
      ...(init.headers || {}),
    },
  };
}

// ---- HEAD (metadata only) ----
export async function HEAD(req: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  try {
    const resolvedParams = await params;
    const segments = resolvedParams.path ?? [];
    if (!segments.length) {
      return new Response(null, { status: 400 });
    }

    // Handle product/index format
    let productName: string;
    let imageIndex = 0;

    if (segments.length === 1) {
      productName = segments[0];
    } else if (segments.length === 2) {
      productName = segments[0];
      imageIndex = parseInt(segments[1]) || 0;
    } else {
      return new Response(null, { status: 400 });
    }

    // Sanitation
    if (!isSafeSegment(productName)) {
      return new Response(null, { status: 400 });
    }

    // Find images for this product
    const productImages = await findProductImages(productName);
    if (!productImages.length || imageIndex >= productImages.length) {
      return new Response(null, { status: 404 });
    }

    const filename = productImages[imageIndex];
    const fullPath = await safeResolveInsideBase(filename);
    if (!fullPath) return new Response(null, { status: 400 });

    const st = await statOrNull(fullPath);
    if (!st || !st.isFile()) return new Response(null, { status: 404 });
    if (st.size > MAX_FILE_BYTES) return new Response(null, { status: 413 });

    const ext = path.extname(filename).toLowerCase();
    const etag = makeEtag(st.size, st.mtimeMs);
    const ifNoneMatch = req.headers.get("if-none-match");
    const ifModifiedSince = req.headers.get("if-modified-since");
    const mtimeUTC = new Date(st.mtimeMs).toUTCString();

    if (ifNoneMatch === etag || (ifModifiedSince && new Date(ifModifiedSince).getTime() >= st.mtimeMs)) {
      return new Response(null, withCommonHeaders({ status: 304, headers: { ETag: etag, "Last-Modified": mtimeUTC } }));
    }

    return new Response(null, withCommonHeaders({
      status: 200,
      headers: {
        ETag: etag,
        "Last-Modified": mtimeUTC,
        "Content-Type": contentTypeFromExt(ext),
        "Content-Length": String(st.size),
        "Accept-Ranges": "bytes",
        "Content-Disposition": `inline; filename="${filename}"`,
      },
    }));
  } catch (err) {
    logger.error("API images HEAD -> Unexpected error:", err);
    return new Response(null, { status: 500 });
  }
}

// ---- GET (streamed, supports range) ----
export async function GET(req: NextRequest, { params }: { params: Promise<{ path: string[] }> }) {
  try {
    const ip = req.headers.get("x-forwarded-for") ?? req.headers.get("x-real-ip") ?? "unknown";
    const paramsValue = await params;
    const segments = paramsValue.path ?? [];
    
    if (!segments.length) {
      logger.error("API images GET -> Missing path", { ip });
      return Response.json({ error: "Missing path" }, { status: 400 });
    }

    // Handle product/index format
    let productName: string;
    let imageIndex = 0;

    if (segments.length === 1) {
      productName = segments[0];
    } else if (segments.length === 2) {
      productName = segments[0];
      imageIndex = parseInt(segments[1]) || 0;
    } else {
      logger.error("API images GET -> Invalid path format", { segments, ip });
      return Response.json({ error: "Invalid path" }, { status: 400 });
    }

    // Sanitation
    if (!isSafeSegment(productName)) {
      logger.error("API images GET -> Unsafe product name", { productName, ip });
      return Response.json({ error: "Invalid product name" }, { status: 400 });
    }

    // Find images for this product
    const productImages = await findProductImages(productName);
    if (!productImages.length) {
      // Instead of returning JSON error, redirect to default image
      try {
        const defaultImagePath = path.join(process.cwd(), 'public', 'productDefault.jpg');
        const defaultStat = await statOrNull(defaultImagePath);
        
        if (defaultStat && defaultStat.isFile()) {
          const stream = fs.createReadStream(defaultImagePath);
          return new Response(stream as unknown as BodyInit, withCommonHeaders({
            status: 200,
            headers: {
              "Content-Type": "image/jpeg",
              "Content-Length": String(defaultStat.size),
              "Accept-Ranges": "bytes",
              "Content-Disposition": `inline; filename="productDefault.jpg"`,
            },
          }));
        }
      } catch (error) {
        logger.error("Failed to serve default image:", error);
      }
      
      // If default image also fails, then return JSON error
      logger.error("API images GET -> No images found for product", { productName, ip });
      return Response.json({ error: "No images found" }, { status: 404 });
    }

    if (imageIndex >= productImages.length) {
      logger.error("API images GET -> Image index out of bounds", { productName, imageIndex, totalImages: productImages.length, ip });
      return Response.json({ error: "Image index out of bounds" }, { status: 404 });
    }

    const filename = productImages[imageIndex];
    const fullPath = await safeResolveInsideBase(filename);
    if (!fullPath) {
      logger.error("API images GET -> Invalid file path", { filename, ip });
      return Response.json({ error: "Invalid path" }, { status: 400 });
    }

    const st = await statOrNull(fullPath);
    if (!st || !st.isFile()) {
      logger.error("API images GET -> File not found", { fullPath, ip });
      return Response.json({ error: "Image not found" }, { status: 404 });
    }

    if (st.size > MAX_FILE_BYTES) {
      logger.error("API images GET -> File too large", { size: st.size, ip });
      return Response.json({ error: "File too large" }, { status: 413 });
    }

    const ext = path.extname(filename).toLowerCase();
    const etag = makeEtag(st.size, st.mtimeMs);
    const ifNoneMatch = req.headers.get("if-none-match");
    const ifModifiedSince = req.headers.get("if-modified-since");
    const mtimeUTC = new Date(st.mtimeMs).toUTCString();

    // Conditional GET → 304
    if (ifNoneMatch === etag || (ifModifiedSince && new Date(ifModifiedSince).getTime() >= st.mtimeMs)) {
      return new Response(null, withCommonHeaders({
        status: 304,
        headers: { ETag: etag, "Last-Modified": mtimeUTC },
      }));
    }

    // Range support
    const range = parseRange(req.headers.get("range"), st.size);
    const contentType = contentTypeFromExt(ext);

    if (range) {
      const { start, end } = range;
      const chunkSize = end - start + 1;

      const stream = fs.createReadStream(fullPath, { start, end });
      return new Response(stream as unknown as BodyInit, withCommonHeaders({
        status: 206,
        headers: {
          "Content-Type": contentType,
          "Content-Length": String(chunkSize),
          "Content-Range": `bytes ${start}-${end}/${st.size}`,
          "Accept-Ranges": "bytes",
          "ETag": etag,
          "Last-Modified": mtimeUTC,
          "Content-Disposition": `inline; filename="${filename}"`,
        },
      }));
    }

    // Full file streaming
    const stream = fs.createReadStream(fullPath);
    return new Response(stream as unknown as BodyInit, withCommonHeaders({
      status: 200,
      headers: {
        "Content-Type": contentType,
        "Content-Length": String(st.size),
        "Accept-Ranges": "bytes",
        "ETag": etag,
        "Last-Modified": mtimeUTC,
        "Content-Disposition": `inline; filename="${filename}"`,
      },
    }));
  } catch (error) {
    logger.error("API images GET -> Unexpected error:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: "Method not allowed" }, { status: 405 });
}



//varianta buna doar cu foldere pentru fiecare cod de piesa si in UI foloseai :      

            //src={
            //   product.ImageUrl?.[selectedImage]
            //     ? `/api/images/${product.ImageUrl[selectedImage]}`
            //     : "/productDefault.jpg"
            // }
            // sau 
            //               src={
            //                 item.image && item.image.startsWith("products/")
            //                   ? `/api/images/${item.image}` 
            //                   : "/productDefault.jpg"      
            //               }





// import { NextRequest, NextResponse } from "next/server";
// import fs from "fs";
// import fsp from "fs/promises";
// import path from "path";
// import { logger } from "@/lib/logger";


// // Ensure Node runtime (fs isn't available on edge)
// export const runtime = "nodejs";

// // --- Config ---
// const BASE_DIR = process.env.PICTURES_BASE_PATH; // e.g. \\NAS\pics or /mnt/pics
// const ALLOWED_EXT = new Set([".jpg", ".jpeg", ".png", ".webp", ".gif", ".avif", ".bmp"]);
// const MAX_FILE_BYTES = 50 * 1024 * 1024; // 50MB guard; tune for your needs
// const MAX_PATH_LENGTH = 300; // avoid absurdly long paths
// const MAX_SEGMENT_LEN = 100; // avoid pathological segments


// // Helpers
// const isSafeSegment = (seg: string) =>
//   seg.length > 0 &&
//   seg.length <= MAX_SEGMENT_LEN &&
//   !seg.includes("\0") &&
//   seg !== "." &&
//   seg !== "..";

// const makeEtag = (size: number, mtimeMs: number) =>
//   `"${size.toString(16)}-${Math.trunc(mtimeMs).toString(16)}"`; // strong enough for static files

// function contentTypeFromExt(ext: string): string {
//   switch (ext) {
//     case ".jpg":
//     case ".jpeg":
//       return "image/jpeg";
//     case ".png":
//       return "image/png";
//     case ".webp":
//       return "image/webp";
//     case ".gif":
//       return "image/gif";
//     case ".avif":
//       return "image/avif";
//     case ".bmp":
//       return "image/bmp";
//     default:
//       return "application/octet-stream";
//   }
// }

// function securityHeaders() {
//   return {
//     "X-Content-Type-Options": "nosniff",
//     // Change these if you will use CDN or proxy
//     "Cross-Origin-Resource-Policy": "same-origin", //cross-origin daca vrem sa fie accesibile de oriunde
//     "Cross-Origin-Embedder-Policy": "require-corp",
//   };
// }

// // Build absolute, symlink-resolved path and ensure it's inside BASE_DIR
// async function safeResolveInsideBase(relativePath: string) {
//   if (!BASE_DIR) {
//     throw new Error("IMAGES_BASE_PATH is not defined");
//   }

//   const baseReal = await fsp.realpath(BASE_DIR);
//   const target = path.join(BASE_DIR, relativePath);
//   const targetReal = await fsp.realpath(target).catch(() => null);
//   if (!targetReal || !targetReal.startsWith(baseReal + path.sep)) return null;
//   return targetReal;
// }

// async function statOrNull(p: string) {
//   try {
//     return await fsp.stat(p);
//   } catch {
//     return null;
//   }
// }

// function parseRange(rangeHeader: string | null, size: number) {
//   if (!rangeHeader || !rangeHeader.startsWith("bytes=")) return null;
//   const [startStr, endStr] = rangeHeader.replace("bytes=", "").split("-", 2);
//   let start = startStr ? Number(startStr) : NaN;
//   let end = endStr ? Number(endStr) : NaN;

//   if (Number.isNaN(start)) {
//     // suffix range: bytes=-500 (last 500 bytes)
//     const last = Number(endStr);
//     if (Number.isNaN(last)) return null;
//     start = Math.max(size - last, 0);
//     end = size - 1;
//   } else {
//     end = Number.isNaN(end) ? size - 1 : Math.min(end, size - 1);
//   }

//   if (start < 0 || end < start || start >= size) return null;
//   return { start, end };
// }

// function withCommonHeaders(
//   init: ResponseInit,
//   cacheSeconds = 31536000 // 1 year
// ): ResponseInit {
//   return {
//     ...init,
//     headers: {
//       "Cache-Control": `public, max-age=${cacheSeconds}, immutable`,
//       ...securityHeaders(),
//       ...(init.headers || {}),
//     },
//   };
// }

// // ---- HEAD (metadata only) ----
// export async function HEAD(req: NextRequest,   { params }: { params: Promise<{ path: string[] }> }) {
//   try {
//     const resolvedParams = await params;
//     const segments = resolvedParams.path ?? [];
//     if (!segments.length) {
//       return new Response(null, { status: 400 });
//     }

//     // sanitation
//     for (const seg of segments) if (!isSafeSegment(seg)) return new Response(null, { status: 400 });

//     const rel = segments.join("/");
//     if (rel.length > MAX_PATH_LENGTH) return new Response(null, { status: 400 });

//     const ext = path.extname(rel).toLowerCase();
//     if (!ALLOWED_EXT.has(ext)) return new Response(null, { status: 415 });

//     const fullPath = await safeResolveInsideBase(rel);
//     if (!fullPath) return new Response(null, { status: 400 });

//     const st = await statOrNull(fullPath);
//     if (!st || !st.isFile()) return new Response(null, { status: 404 });
//     if (st.size > MAX_FILE_BYTES) return new Response(null, { status: 413 });

//     const etag = makeEtag(st.size, st.mtimeMs);
//     const ifNoneMatch = req.headers.get("if-none-match");
//     const ifModifiedSince = req.headers.get("if-modified-since");
//     const mtimeUTC = new Date(st.mtimeMs).toUTCString();

//     // Conditional HEAD
//     if (ifNoneMatch === etag || (ifModifiedSince && new Date(ifModifiedSince).getTime() >= st.mtimeMs)) {
//       return new Response(null, withCommonHeaders({ status: 304, headers: { ETag: etag, "Last-Modified": mtimeUTC } }));
//     }

//     return new Response(null, withCommonHeaders({
//       status: 200,
//       headers: {
//         ETag: etag,
//         "Last-Modified": mtimeUTC,
//         "Content-Type": contentTypeFromExt(ext),
//         "Content-Length": String(st.size),
//         "Accept-Ranges": "bytes",
//         "Content-Disposition": `inline; filename="${path.basename(fullPath)}"`,
//       },
//     }));
//   } catch (err) {
//     logger.error("API images HEAD -> Unexpected error:", err);
//     return new Response(null, { status: 500 });
//   }
// }

// // ---- GET (streamed, supports range) ----
// export async function GET(req: NextRequest,   { params }: { params: Promise<{ path: string[] }> }) {
//   try {
//     const ip = req.headers.get("x-forwarded-for") ?? req.headers.get("x-real-ip") ?? "unknown";
//     const paramsValue = await params;
//     const segments = paramsValue.path ?? [];
//     if (!segments.length) {
//       logger.error("API images GET -> Missing path", { ip });
//       return Response.json({ error: "Missing path" }, { status: 400 });
//     }

//     // sanitation
//     for (const seg of segments) {
//       if (!isSafeSegment(seg)) {
//         logger.error("API images GET -> Unsafe segment", { seg, ip });
//         return Response.json({ error: "Invalid path" }, { status: 400 });
//       }
//     }

//     const rel = segments.join("/");
//     if (rel.length > MAX_PATH_LENGTH) {
//       logger.error("API images GET -> Path too long", { relLen: rel.length, ip });
//       return Response.json({ error: "Invalid path" }, { status: 400 });
//     }

//     const ext = path.extname(rel).toLowerCase();
//     if (!ALLOWED_EXT.has(ext)) {
//       logger.error("API images GET -> Disallowed extension", { ext, ip });
//       return Response.json({ error: "Unsupported media type" }, { status: 415 });
//     }

//     const fullPath = await safeResolveInsideBase(rel);
//     if (!fullPath) {
//       logger.error("API images GET -> Traversal attempt", { rel, ip });
//       return Response.json({ error: "Invalid path" }, { status: 400 });
//     }

//     const st = await statOrNull(fullPath);
//     if (!st || !st.isFile()) {
//       logger.error("API images GET -> File not found", { fullPath, ip });
//       return Response.json({ error: "Image not found" }, { status: 404 });
//     }

//     if (st.size > MAX_FILE_BYTES) {
//       logger.error("API images GET -> File too large", { size: st.size, ip });
//       return Response.json({ error: "File too large" }, { status: 413 });
//     }

//     const etag = makeEtag(st.size, st.mtimeMs);
//     const ifNoneMatch = req.headers.get("if-none-match");
//     const ifModifiedSince = req.headers.get("if-modified-since");
//     const mtimeUTC = new Date(st.mtimeMs).toUTCString();

//     // Conditional GET → 304
//     if (ifNoneMatch === etag || (ifModifiedSince && new Date(ifModifiedSince).getTime() >= st.mtimeMs)) {
//       return new Response(null, withCommonHeaders({
//         status: 304,
//         headers: { ETag: etag, "Last-Modified": mtimeUTC },
//       }));
//     }

//     // Range support
//     const range = parseRange(req.headers.get("range"), st.size);
//     const contentType = contentTypeFromExt(ext);
//     const filename = path.basename(fullPath);

//     if (range) {
//       const { start, end } = range;
//       const chunkSize = end - start + 1;

//       const stream = fs.createReadStream(fullPath, { start, end });
//       return new Response(stream as unknown as BodyInit, withCommonHeaders({
//         status: 206,
//         headers: {
//           "Content-Type": contentType,
//           "Content-Length": String(chunkSize),
//           "Content-Range": `bytes ${start}-${end}/${st.size}`,
//           "Accept-Ranges": "bytes",
//           "ETag": etag,
//           "Last-Modified": mtimeUTC,
//           "Content-Disposition": `inline; filename="${filename}"`,
//         },
//       }));
//     }

//     // Full file streaming
//     const stream = fs.createReadStream(fullPath);
//     return new Response(stream as unknown as BodyInit, withCommonHeaders({
//       status: 200,
//       headers: {
//         "Content-Type": contentType,
//         "Content-Length": String(st.size),
//         "Accept-Ranges": "bytes",
//         "ETag": etag,
//         "Last-Modified": mtimeUTC,
//         "Content-Disposition": `inline; filename="${filename}"`,
//       },
//     }));
//   } catch (error) {
//     logger.error("API images GET -> Unexpected error:", error);
//     return Response.json({ error: "Internal server error" }, { status: 500 });
//   }
// }

// // Handle unsupported methods
// export async function POST() {
//   return NextResponse.json(
//     { error: "Method not allowed" },
//     { status: 405 }
//   );
// }

// export async function PUT() {
//   return NextResponse.json(
//     { error: "Method not allowed" },
//     { status: 405 }
//   );
// }

// export async function DELETE() {
//   return NextResponse.json(
//     { error: "Method not allowed" },
//     { status: 405 }
//   );
// }