"use client"

import React, { useState, useCallback, useRef, useEffect } from "react"
import { cn } from "@/lib/utils"
import { MegaMenuProps, MegaMenuState, MegaMenuAnimationConfig } from "@/types/mega-menu"
import { MegaMenuItem, MegaMenuItemList, MegaMenuItemSkeleton } from "./MegaMenuItem"
import { MegaMenuContent } from "./MegaMenuContent"

// Animation configuration
const ANIMATION_CONFIG: MegaMenuAnimationConfig = {
  hoverDelay: 150,    // Delay before showing dropdown
  hideDelay: 300,     // Delay before hiding dropdown
  animationDuration: 200
}

/**
 * MegaMenu - Main container component for the mega-menu system
 * Manages hover state, timing, and coordinates between triggers and content
 * Provides smooth animations and proper UX for large category hierarchies
 */
export function MegaMenu({ categories, className }: MegaMenuProps) {
  const [state, setState] = useState<MegaMenuState>({
    activeCategory: null,
    isVisible: false,
    hoverTimeout: null
  })

  const containerRef = useRef<HTMLDivElement>(null)

  // Clear timeout on unmount
  useEffect(() => {
    return () => {
      if (state.hoverTimeout) {
        clearTimeout(state.hoverTimeout)
      }
    }
  }, [state.hoverTimeout])

  // Handle category hover with delay
  const handleCategoryHover = useCallback((categoryId: string | null) => {
    // Clear existing timeout
    if (state.hoverTimeout) {
      clearTimeout(state.hoverTimeout)
    }

    if (categoryId) {
      // Show dropdown after delay
      const timeout = setTimeout(() => {
        setState(prev => ({
          ...prev,
          activeCategory: categoryId,
          isVisible: true,
          hoverTimeout: null
        }))
      }, ANIMATION_CONFIG.hoverDelay)

      setState(prev => ({
        ...prev,
        hoverTimeout: timeout
      }))
    } else {
      // Hide dropdown after delay
      const timeout = setTimeout(() => {
        setState(prev => ({
          ...prev,
          activeCategory: null,
          isVisible: false,
          hoverTimeout: null
        }))
      }, ANIMATION_CONFIG.hideDelay)

      setState(prev => ({
        ...prev,
        hoverTimeout: timeout
      }))
    }
  }, [state.hoverTimeout])

  // Handle immediate hide (when clicking outside or navigating)
  const handleImmediateHide = useCallback(() => {
    if (state.hoverTimeout) {
      clearTimeout(state.hoverTimeout)
    }
    
    setState({
      activeCategory: null,
      isVisible: false,
      hoverTimeout: null
    })
  }, [state.hoverTimeout])

  // Handle container mouse leave
  const handleContainerLeave = useCallback(() => {
    handleCategoryHover(null)
  }, [handleCategoryHover])

  // Find active category data
  const activeCategory = categories.find(cat => cat.id === state.activeCategory) || null

  // Loading state
  if (!categories || categories.length === 0) {
    return (
      <div className={cn("relative", className)}>
        <MegaMenuItemList>
          {Array.from({ length: 5 }).map((_, i) => (
            <MegaMenuItemSkeleton key={i} />
          ))}
        </MegaMenuItemList>
      </div>
    )
  }

  return (
    <div 
      ref={containerRef}
      className={cn("relative", className)}
      onMouseLeave={handleContainerLeave}
    >
      {/* Category Triggers */}
      <MegaMenuItemList>
        {categories.map((category) => (
          <MegaMenuItem
            key={category.id}
            category={category}
            isActive={state.activeCategory === category.id}
            onHover={handleCategoryHover}
          />
        ))}
      </MegaMenuItemList>

      {/* Dropdown Content */}
      <MegaMenuContent
        category={activeCategory}
        isVisible={state.isVisible}
        onClose={handleImmediateHide}
      />
    </div>
  )
}

/**
 * MegaMenuSkeleton - Loading state for the entire mega-menu
 */
export function MegaMenuSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("relative", className)}>
      <MegaMenuItemList>
        {Array.from({ length: 6 }).map((_, i) => (
          <MegaMenuItemSkeleton key={i} />
        ))}
      </MegaMenuItemList>
    </div>
  )
}

/**
 * MegaMenuError - Error state component
 */
export function MegaMenuError({
  onRetry,
  className
}: {
  error?: Error
  onRetry?: () => void
  className?: string
}) {
  return (
    <div className={cn("relative py-2 px-4", className)}>
      <div className="text-sm text-red-600 dark:text-red-400">
        Eroare la încărcarea categoriilor
      </div>
      {onRetry && (
        <button
          onClick={onRetry}
          className="text-xs text-blue-600 dark:text-blue-400 hover:underline ml-2"
        >
          Încearcă din nou
        </button>
      )}
    </div>
  )
}
