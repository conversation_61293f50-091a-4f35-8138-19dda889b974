"use client"

import React from "react"
import Link from "next/link"
import { motion, AnimatePresence } from "framer-motion"
import { ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { MegaMenuContentProps } from "@/types/mega-menu"

/**
 * MegaMenuContent - The large dropdown panel that displays category hierarchy
 * Shows categoryLevel2 and categoryLevel3 in a responsive grid layout
 * Optimized for displaying ~100 items per level1 category with small fonts
 */
export function MegaMenuContent({ 
  category, 
  isVisible, 
  onClose, 
  className 
}: MegaMenuContentProps) {
  if (!category) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className={cn(
            "absolute top-full left-0 w-full bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm",
            "shadow-2xl border-t border-gray-200 dark:border-gray-700 z-[60]",
            "max-h-[80vh] overflow-y-auto",
            "before:absolute before:inset-0 before:bg-gradient-to-b before:from-transparent before:to-white/5 before:pointer-events-none",
            className
          )}
          onMouseLeave={onClose}
        >
          <div className="max-w-[1640px] mx-auto px-6 py-8">

            {/* Level 2 Categories Grid */}
            <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-8">
              {category.level2Categories.map((level2, index) => (
                <motion.div
                  key={level2.id}
                  className="space-y-3 break-inside-avoid mb-8"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    duration: 0.2,
                    delay: index * 0.02,
                    ease: "easeOut",
                  }}
                >
                  {/* Level 2 Category Header */}
                  <Link
                    //href={generateCategoryUrl(level2.nameRO)}
                    href={`/category/${level2.slug}`}
                    onClick={onClose}
                    className="group flex items-center space-x-2 text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
                  >
                    <span className="font-semibold text-sm">
                      {level2.nameRO || level2.name}
                    </span>
                    <ChevronRight className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                  </Link>

                  {/* Level 3 Categories List */}
                  {level2.level3Categories.length > 0 && (
                    <div className="space-y-1 pl-2">
                      {level2.level3Categories.map((level3) => (
                        <Link
                          key={level3.id}
                          //href={generateCategoryUrl(level2.nameRO, level3.nameRO)}
                          href={`/category/${level2.slug}?category3=${level3.slug}`}
                          onClick={onClose}
                          className="group flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1"
                        >
                          <span className="truncate pr-2">
                            {level3.nameRO || level3.name}
                          </span>
                          {level3.productCount > 0 && (
                            <span className="text-xs text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full flex-shrink-0">
                              {level3.productCount}
                            </span>
                          )}
                        </Link>
                      ))}
                    </div>
                  )}

                  {/* "View All" link  */}
                  {level2.level3Categories.length > 4 && (
                    <Link
                      href={`/category/${level2.slug}`}
                      onClick={onClose}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium pl-2 inline-flex items-center space-x-1 transition-colors duration-200"
                    >
                      <span>Vezi toate</span>
                      <ChevronRight className="h-3 w-3" />
                    </Link>
                  )}
                </motion.div>
              ))}
            </div>

            {/* Footer with total categories count */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                <span>
                  {category.level2Categories.length} categorii principale
                </span>
                <span>
                  {category.level2Categories.reduce(
                    (total, level2) => total + level2.level3Categories.length, 
                    0
                  )} subcategorii
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

/**
 * Skeleton loader for MegaMenuContent
 * Shows while category data is loading
 */
export function MegaMenuContentSkeleton() {
  return (
    <div className="absolute top-full left-0 w-full bg-white dark:bg-gray-900 shadow-2xl border-t border-gray-200 dark:border-gray-700 z-[60]">
      <div className="max-w-[1640px] mx-auto px-6 py-8">
        {/* Header Skeleton */}
        <div className="mb-6">
          <div className="h-6 w-48 bg-gray-200 dark:bg-gray-700 rounded animate-pulse mb-2"></div>
          <div className="h-1 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>

        {/* Grid Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-4 w-32 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
              <div className="space-y-1 pl-2">
                {Array.from({ length: 5 }).map((_, j) => (
                  <div key={j} className="h-3 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

            // <div className="grid grid-cols-[repeat(auto-fit,minmax(300px,1fr))] gap-8">
            //   {category.level2Categories.map((level2, index) => (
            //     <motion.div
            //       key={level2.id}
            //       className="space-y-3"
            //       initial={{ opacity: 0, y: 10 }}
            //       animate={{ opacity: 1, y: 0 }}
            //       transition={{
            //         duration: 0.2,
            //         delay: index * 0.05,
            //         ease: "easeOut"
            //       }}
            //     >

            //       <Link
            //         href={generateCategoryUrl(level2.nameRO)}
            //         onClick={onClose}
            //         className="group flex items-center space-x-2 text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200"
            //       >
            //         <span className="font-semibold text-sm">
            //           {level2.nameRO || level2.name}
            //         </span>
            //         <ChevronRight className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
            //       </Link>

            //       {level2.level3Categories.length > 0 && (
            //         <div className="space-y-1 pl-2">
            //           {level2.level3Categories.map((level3) => (
            //             <Link
            //               key={level3.id}
            //               href={generateCategoryUrl(level2.nameRO, level3.nameRO)}
            //               onClick={onClose}
            //               className="group flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 py-1"
            //             >
            //               <span className="truncate pr-2">
            //                 {level3.nameRO || level3.name}
            //               </span>
            //               {level3.productCount > 0 && (
            //                 <span className="text-xs text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-800 px-2 py-0.5 rounded-full flex-shrink-0">
            //                   {level3.productCount}
            //                 </span>
            //               )}
            //             </Link>
            //           ))}
            //         </div>
            //       )}


            //       {level2.level3Categories.length > 4 && (
            //         <Link
            //           href={generateCategoryUrl(level2.nameRO)}
            //           onClick={onClose}
            //           className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium pl-2 inline-flex items-center space-x-1 transition-colors duration-200"
            //         >
            //           <span>Vezi toate</span>
            //           <ChevronRight className="h-3 w-3" />
            //         </Link>
            //       )}
            //     </motion.div>
            //   ))}
            // </div>