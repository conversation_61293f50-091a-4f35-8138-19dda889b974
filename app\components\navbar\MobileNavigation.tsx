"use client"

import React from "react"
import { X, Menu } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { MobileNavigationProps } from "@/types/mega-menu"
import { MobileCategoryList } from "./MobileCategoryList"

/**
 * MobileNavigation - Hamburger menu for mobile devices
 * Uses shadcn Sheet component for slide-out navigation
 * Contains the full category hierarchy in a mobile-optimized layout
 */
export function MobileNavigation({ categories, isOpen, onClose }: MobileNavigationProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      
      <SheetContent side="left" className="w-[300px] sm:w-[350px] p-0">
        <SheetHeader className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-lg font-bold">
              Categorii
            </SheetTitle>
          </div>
          <SheetDescription className="text-sm text-gray-600 dark:text-gray-400">
            Explorează toate categoriile de piese auto BMW
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto">
          <MobileCategoryList 
            categories={categories} 
            onCategoryClick={onClose}
          />
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
            {categories.length} categorii principale disponibile
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

/**
 * MobileNavigationTrigger - Standalone hamburger button
 * Can be used independently when you need just the trigger
 */
export function MobileNavigationTrigger({ 
  onClick, 
  className 
}: { 
  onClick: () => void
  className?: string 
}) {
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={onClick}
      className={`md:hidden w-9 h-9 ${className}`}
      aria-label="Deschide meniul"
    >
      <Menu className="h-5 w-5" />
    </Button>
  )
}

/**
 * MobileNavigationSkeleton - Loading state for mobile navigation
 */
export function MobileNavigationSkeleton() {
  return (
    <div className="md:hidden">
      <div className="w-9 h-9 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
    </div>
  )
}
