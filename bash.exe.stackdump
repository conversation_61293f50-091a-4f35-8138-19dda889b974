Stack trace:
Frame         Function      Args
0007FFFF8E70  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF7D70) msys-2.0.dll+0x1FE8E
0007FFFF8E70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9148) msys-2.0.dll+0x67F9
0007FFFF8E70  000210046832 (000210286019, 0007FFFF8D28, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF8E70  000210068E24 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9150  00021006A225 (0007FFFF8E80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFC69E0000 ntdll.dll
7FFFC5D00000 KERNEL32.DLL
7FFFC4230000 KERNELBASE.dll
0000671C0000 sysfer.dll
7FFFC6590000 USER32.dll
7FFFC4200000 win32u.dll
7FFFC4DE0000 GDI32.dll
7FFFC4630000 gdi32full.dll
7FFFC3D50000 msvcp_win.dll
7FFFC40B0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFC6040000 advapi32.dll
7FFFC55E0000 msvcrt.dll
7FFFC57E0000 sechost.dll
7FFFC4B00000 RPCRT4.dll
7FFFC3000000 CRYPTBASE.DLL
7FFFC4010000 bcryptPrimitives.dll
7FFFC5EC0000 IMM32.DLL
